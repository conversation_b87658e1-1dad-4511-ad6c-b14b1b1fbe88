import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/vanta/dist/vanta.dots.min.js
var require_vanta_dots_min = __commonJS({
  "node_modules/vanta/dist/vanta.dots.min.js"(exports, module) {
    !(function(t, e) {
      "object" == typeof exports && "object" == typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define([], e) : "object" == typeof exports ? exports._vantaEffect = e() : t._vantaEffect = e();
    })("undefined" != typeof self ? self : exports, (() => (() => {
      "use strict";
      var t = { d: (e2, i2) => {
        for (var s2 in i2) t.o(i2, s2) && !t.o(e2, s2) && Object.defineProperty(e2, s2, { enumerable: true, get: i2[s2] });
      }, o: (t2, e2) => Object.prototype.hasOwnProperty.call(t2, e2), r: (t2) => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t2, "__esModule", { value: true });
      } }, e = {};
      function i(t2, e2) {
        return null == t2 && (t2 = 0), null == e2 && (e2 = 1), t2 + Math.random() * (e2 - t2);
      }
      t.r(e), t.d(e, { default: () => d }), Number.prototype.clamp = function(t2, e2) {
        return Math.min(Math.max(this, t2), e2);
      };
      function s(t2) {
        for (; t2.children && t2.children.length > 0; ) s(t2.children[0]), t2.remove(t2.children[0]);
        t2.geometry && t2.geometry.dispose(), t2.material && (Object.keys(t2.material).forEach(((e2) => {
          t2.material[e2] && null !== t2.material[e2] && "function" == typeof t2.material[e2].dispose && t2.material[e2].dispose();
        })), t2.material.dispose());
      }
      const o = "object" == typeof window;
      let n = o && window.THREE || {};
      o && !window.VANTA && (window.VANTA = {});
      const r = o && window.VANTA || {};
      r.register = (t2, e2) => r[t2] = (t3) => new e2(t3), r.version = "0.5.24";
      const h = function() {
        return Array.prototype.unshift.call(arguments, "[VANTA]"), console.error.apply(this, arguments);
      };
      r.VantaBase = class {
        constructor(t2 = {}) {
          if (!o) return false;
          r.current = this, this.windowMouseMoveWrapper = this.windowMouseMoveWrapper.bind(this), this.windowTouchWrapper = this.windowTouchWrapper.bind(this), this.windowGyroWrapper = this.windowGyroWrapper.bind(this), this.resize = this.resize.bind(this), this.animationLoop = this.animationLoop.bind(this), this.restart = this.restart.bind(this);
          const e2 = "function" == typeof this.getDefaultOptions ? this.getDefaultOptions() : this.defaultOptions;
          if (this.options = Object.assign({ mouseControls: true, touchControls: true, gyroControls: false, minHeight: 200, minWidth: 200, scale: 1, scaleMobile: 1 }, e2), (t2 instanceof HTMLElement || "string" == typeof t2) && (t2 = { el: t2 }), Object.assign(this.options, t2), this.options.THREE && (n = this.options.THREE), this.el = this.options.el, null == this.el) h('Instance needs "el" param!');
          else if (!(this.options.el instanceof HTMLElement)) {
            const t3 = this.el;
            if (this.el = (i2 = t3, document.querySelector(i2)), !this.el) return void h("Cannot find element", t3);
          }
          var i2, s2;
          this.prepareEl(), this.initThree(), this.setSize();
          try {
            this.init();
          } catch (t3) {
            return h("Init error", t3), this.renderer && this.renderer.domElement && this.el.removeChild(this.renderer.domElement), void (this.options.backgroundColor && (console.log("[VANTA] Falling back to backgroundColor"), this.el.style.background = (s2 = this.options.backgroundColor, "number" == typeof s2 ? "#" + ("00000" + s2.toString(16)).slice(-6) : s2)));
          }
          this.initMouse(), this.resize(), this.animationLoop();
          const a2 = window.addEventListener;
          a2("resize", this.resize), window.requestAnimationFrame(this.resize), this.options.mouseControls && (a2("scroll", this.windowMouseMoveWrapper), a2("mousemove", this.windowMouseMoveWrapper)), this.options.touchControls && (a2("touchstart", this.windowTouchWrapper), a2("touchmove", this.windowTouchWrapper)), this.options.gyroControls && a2("deviceorientation", this.windowGyroWrapper);
        }
        setOptions(t2 = {}) {
          Object.assign(this.options, t2), this.triggerMouseMove();
        }
        prepareEl() {
          let t2, e2;
          if ("undefined" != typeof Node && Node.TEXT_NODE) for (t2 = 0; t2 < this.el.childNodes.length; t2++) {
            const e3 = this.el.childNodes[t2];
            if (e3.nodeType === Node.TEXT_NODE) {
              const t3 = document.createElement("span");
              t3.textContent = e3.textContent, e3.parentElement.insertBefore(t3, e3), e3.remove();
            }
          }
          for (t2 = 0; t2 < this.el.children.length; t2++) e2 = this.el.children[t2], "static" === getComputedStyle(e2).position && (e2.style.position = "relative"), "auto" === getComputedStyle(e2).zIndex && (e2.style.zIndex = 1);
          "static" === getComputedStyle(this.el).position && (this.el.style.position = "relative");
        }
        applyCanvasStyles(t2, e2 = {}) {
          Object.assign(t2.style, { position: "absolute", zIndex: 0, top: 0, left: 0, background: "" }), Object.assign(t2.style, e2), t2.classList.add("vanta-canvas");
        }
        initThree() {
          n.WebGLRenderer ? (this.renderer = new n.WebGLRenderer({ alpha: true, antialias: true }), this.el.appendChild(this.renderer.domElement), this.applyCanvasStyles(this.renderer.domElement), isNaN(this.options.backgroundAlpha) && (this.options.backgroundAlpha = 1), this.scene = new n.Scene()) : console.warn("[VANTA] No THREE defined on window");
        }
        getCanvasElement() {
          return this.renderer ? this.renderer.domElement : this.p5renderer ? this.p5renderer.canvas : void 0;
        }
        getCanvasRect() {
          const t2 = this.getCanvasElement();
          return !!t2 && t2.getBoundingClientRect();
        }
        windowMouseMoveWrapper(t2) {
          const e2 = this.getCanvasRect();
          if (!e2) return false;
          const i2 = t2.clientX - e2.left, s2 = t2.clientY - e2.top;
          i2 >= 0 && s2 >= 0 && i2 <= e2.width && s2 <= e2.height && (this.mouseX = i2, this.mouseY = s2, this.options.mouseEase || this.triggerMouseMove(i2, s2));
        }
        windowTouchWrapper(t2) {
          const e2 = this.getCanvasRect();
          if (!e2) return false;
          if (1 === t2.touches.length) {
            const i2 = t2.touches[0].clientX - e2.left, s2 = t2.touches[0].clientY - e2.top;
            i2 >= 0 && s2 >= 0 && i2 <= e2.width && s2 <= e2.height && (this.mouseX = i2, this.mouseY = s2, this.options.mouseEase || this.triggerMouseMove(i2, s2));
          }
        }
        windowGyroWrapper(t2) {
          const e2 = this.getCanvasRect();
          if (!e2) return false;
          const i2 = Math.round(2 * t2.alpha) - e2.left, s2 = Math.round(2 * t2.beta) - e2.top;
          i2 >= 0 && s2 >= 0 && i2 <= e2.width && s2 <= e2.height && (this.mouseX = i2, this.mouseY = s2, this.options.mouseEase || this.triggerMouseMove(i2, s2));
        }
        triggerMouseMove(t2, e2) {
          void 0 === t2 && void 0 === e2 && (this.options.mouseEase ? (t2 = this.mouseEaseX, e2 = this.mouseEaseY) : (t2 = this.mouseX, e2 = this.mouseY)), this.uniforms && (this.uniforms.iMouse.value.x = t2 / this.scale, this.uniforms.iMouse.value.y = e2 / this.scale);
          const i2 = t2 / this.width, s2 = e2 / this.height;
          "function" == typeof this.onMouseMove && this.onMouseMove(i2, s2);
        }
        setSize() {
          this.scale || (this.scale = 1), "undefined" != typeof navigator && (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth < 600) && this.options.scaleMobile ? this.scale = this.options.scaleMobile : this.options.scale && (this.scale = this.options.scale), this.width = Math.max(this.el.offsetWidth, this.options.minWidth), this.height = Math.max(this.el.offsetHeight, this.options.minHeight);
        }
        initMouse() {
          (!this.mouseX && !this.mouseY || this.mouseX === this.options.minWidth / 2 && this.mouseY === this.options.minHeight / 2) && (this.mouseX = this.width / 2, this.mouseY = this.height / 2, this.triggerMouseMove(this.mouseX, this.mouseY));
        }
        resize() {
          this.setSize(), this.camera && (this.camera.aspect = this.width / this.height, "function" == typeof this.camera.updateProjectionMatrix && this.camera.updateProjectionMatrix()), this.renderer && (this.renderer.setSize(this.width, this.height), this.renderer.setPixelRatio(window.devicePixelRatio / this.scale)), "function" == typeof this.onResize && this.onResize();
        }
        isOnScreen() {
          const t2 = this.el.offsetHeight, e2 = this.el.getBoundingClientRect(), i2 = window.pageYOffset || (document.documentElement || document.body.parentNode || document.body).scrollTop, s2 = e2.top + i2;
          return s2 - window.innerHeight <= i2 && i2 <= s2 + t2;
        }
        animationLoop() {
          this.t || (this.t = 0), this.t2 || (this.t2 = 0);
          const t2 = performance.now();
          if (this.prevNow) {
            let e2 = (t2 - this.prevNow) / (1e3 / 60);
            e2 = Math.max(0.2, Math.min(e2, 5)), this.t += e2, this.t2 += (this.options.speed || 1) * e2, this.uniforms && (this.uniforms.iTime.value = 0.016667 * this.t2);
          }
          return this.prevNow = t2, this.options.mouseEase && (this.mouseEaseX = this.mouseEaseX || this.mouseX || 0, this.mouseEaseY = this.mouseEaseY || this.mouseY || 0, Math.abs(this.mouseEaseX - this.mouseX) + Math.abs(this.mouseEaseY - this.mouseY) > 0.1 && (this.mouseEaseX += 0.05 * (this.mouseX - this.mouseEaseX), this.mouseEaseY += 0.05 * (this.mouseY - this.mouseEaseY), this.triggerMouseMove(this.mouseEaseX, this.mouseEaseY))), (this.isOnScreen() || this.options.forceAnimate) && ("function" == typeof this.onUpdate && this.onUpdate(), this.scene && this.camera && (this.renderer.render(this.scene, this.camera), this.renderer.setClearColor(this.options.backgroundColor, this.options.backgroundAlpha)), this.fps && this.fps.update && this.fps.update(), "function" == typeof this.afterRender && this.afterRender()), this.req = window.requestAnimationFrame(this.animationLoop);
        }
        restart() {
          if (this.scene) for (; this.scene.children.length; ) this.scene.remove(this.scene.children[0]);
          "function" == typeof this.onRestart && this.onRestart(), this.init();
        }
        init() {
          "function" == typeof this.onInit && this.onInit();
        }
        destroy() {
          "function" == typeof this.onDestroy && this.onDestroy();
          const t2 = window.removeEventListener;
          t2("touchstart", this.windowTouchWrapper), t2("touchmove", this.windowTouchWrapper), t2("scroll", this.windowMouseMoveWrapper), t2("mousemove", this.windowMouseMoveWrapper), t2("deviceorientation", this.windowGyroWrapper), t2("resize", this.resize), window.cancelAnimationFrame(this.req);
          const e2 = this.scene;
          e2 && e2.children && s(e2), this.renderer && (this.renderer.domElement && this.el.removeChild(this.renderer.domElement), this.renderer = null, this.scene = null), r.current === this && (r.current = null);
        }
      };
      const a = r.VantaBase;
      let l = "object" == typeof window && window.THREE;
      class c extends a {
        static initClass() {
          this.prototype.defaultOptions = { color: 16746528, color2: 16746528, backgroundColor: 2236962, size: 3, spacing: 35, showLines: true };
        }
        onInit() {
          var t2 = this.camera = new l.PerspectiveCamera(50, this.width / this.height, 0.1, 5e3);
          t2.position.x = 0, t2.position.y = 250, t2.position.z = 50, t2.tx = 0, t2.ty = 50, t2.tz = 350, t2.lookAt(0, 0, 0), this.scene.add(t2);
          var e2, s2, o2, n2, r2, h2, a2, c2 = this.starsGeometry = new l.BufferGeometry(), d2 = this.options.spacing;
          const p = [];
          for (e2 = o2 = -30; o2 <= 30; e2 = ++o2) for (s2 = n2 = -30; n2 <= 30; s2 = ++n2) (r2 = new l.Vector3()).x = e2 * d2 + d2 / 2, r2.y = i(0, 5) - 150, r2.z = s2 * d2 + d2 / 2, p.push(r2);
          if (c2.setFromPoints(p), h2 = new l.PointsMaterial({ color: this.options.color, size: this.options.size }), a2 = this.starField = new l.Points(c2, h2), this.scene.add(a2), this.options.showLines) {
            var u = new l.LineBasicMaterial({ color: this.options.color2 }), m = new l.BufferGeometry();
            const t3 = [];
            for (e2 = 0; e2 < 200; e2++) {
              var f = i(40, 60), w = f + i(12, 20), g = i(-1, 1), y = Math.sqrt(1 - g * g), v = i(0, 2 * Math.PI), M = Math.sin(v) * y, b = Math.cos(v) * y;
              t3.push(new l.Vector3(b * f, M * f, g * f)), t3.push(new l.Vector3(b * w, M * w, g * w));
            }
            m.setFromPoints(t3), this.linesMesh = new l.LineSegments(m, u), this.scene.add(this.linesMesh);
          }
        }
        onUpdate() {
          const t2 = this.starsGeometry;
          this.starField;
          for (var e2 = 0; e2 < t2.attributes.position.array.length; e2 += 3) {
            const i3 = t2.attributes.position.array[e2], s3 = t2.attributes.position.array[e2 + 1], o2 = t2.attributes.position.array[e2 + 2], n2 = s3 + 0.1 * Math.sin(0.02 * o2 + 0.015 * i3 + 0.02 * this.t);
            t2.attributes.position.array[e2 + 1] = n2;
          }
          t2.attributes.position.setUsage(l.DynamicDrawUsage), t2.computeVertexNormals(), t2.attributes.position.needsUpdate = true;
          const i2 = this.camera, s2 = 3e-3;
          i2.position.x += (i2.tx - i2.position.x) * s2, i2.position.y += (i2.ty - i2.position.y) * s2, i2.position.z += (i2.tz - i2.position.z) * s2, i2.lookAt(0, 0, 0), this.linesMesh && (this.linesMesh.rotation.z += 2e-3, this.linesMesh.rotation.x += 8e-4, this.linesMesh.rotation.y += 5e-4);
        }
        onMouseMove(t2, e2) {
          this.camera.tx = 100 * (t2 - 0.5), this.camera.ty = 50 + 50 * e2;
        }
        onRestart() {
          this.scene.remove(this.starField);
        }
      }
      c.initClass();
      const d = r.register("DOTS", c);
      return e;
    })()));
  }
});
export default require_vanta_dots_min();
//# sourceMappingURL=vanta_dist_vanta__dots__min.js.map
