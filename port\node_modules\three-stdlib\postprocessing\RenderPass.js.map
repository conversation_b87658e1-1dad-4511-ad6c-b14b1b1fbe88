{"version": 3, "file": "RenderPass.js", "sources": ["../../src/postprocessing/RenderPass.ts"], "sourcesContent": ["import { Camera, Color, Material, Scene, WebGLRenderTarget, WebGLRenderer } from 'three'\nimport { Pass } from './Pass'\n\nclass RenderPass extends Pass {\n  public scene: Scene\n  public camera: Camera\n  public overrideMaterial: Material | undefined\n  public clearColor: Color | undefined\n  public clearAlpha: number\n  public clearDepth = false\n  private _oldClearColor = new Color()\n\n  constructor(scene: Scene, camera: Camera, overrideMaterial?: Material, clearColor?: Color, clearAlpha = 0) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.overrideMaterial = overrideMaterial\n\n    this.clearColor = clearColor\n    this.clearAlpha = clearAlpha\n\n    this.clear = true\n    this.needsSwap = false\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    let oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    let oldClearAlpha\n    let oldOverrideMaterial: Material | null = null\n\n    if (this.overrideMaterial !== undefined) {\n      oldOverrideMaterial = this.scene.overrideMaterial\n\n      this.scene.overrideMaterial = this.overrideMaterial\n    }\n\n    if (this.clearColor) {\n      renderer.getClearColor(this._oldClearColor)\n      oldClearAlpha = renderer.getClearAlpha()\n\n      renderer.setClearColor(this.clearColor, this.clearAlpha)\n    }\n\n    if (this.clearDepth) {\n      renderer.clearDepth()\n    }\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n\n    // TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n    if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil)\n    renderer.render(this.scene, this.camera)\n\n    if (this.clearColor) {\n      renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n    }\n\n    if (this.overrideMaterial !== undefined) {\n      this.scene.overrideMaterial = oldOverrideMaterial\n    }\n\n    renderer.autoClear = oldAutoClear\n  }\n}\n\nexport { RenderPass }\n"], "names": [], "mappings": ";;;;;;;;AAGA,MAAM,mBAAmB,KAAK;AAAA,EAS5B,YAAY,OAAc,QAAgB,kBAA6B,YAAoB,aAAa,GAAG;AACnG;AATD;AACA;AACA;AACA;AACA;AACA,sCAAa;AACZ,0CAAiB,IAAI;AAK3B,SAAK,QAAQ;AACb,SAAK,SAAS;AAEd,SAAK,mBAAmB;AAExB,SAAK,aAAa;AAClB,SAAK,aAAa;AAElB,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACnB;AAAA,EAEO,OACL,UACA,aACA,YACM;AACN,QAAI,eAAe,SAAS;AAC5B,aAAS,YAAY;AAEjB,QAAA;AACJ,QAAI,sBAAuC;AAEvC,QAAA,KAAK,qBAAqB,QAAW;AACvC,4BAAsB,KAAK,MAAM;AAE5B,WAAA,MAAM,mBAAmB,KAAK;AAAA,IACrC;AAEA,QAAI,KAAK,YAAY;AACV,eAAA,cAAc,KAAK,cAAc;AAC1C,sBAAgB,SAAS;AAEzB,eAAS,cAAc,KAAK,YAAY,KAAK,UAAU;AAAA,IACzD;AAEA,QAAI,KAAK,YAAY;AACnB,eAAS,WAAW;AAAA,IACtB;AAEA,aAAS,gBAAgB,KAAK,iBAAiB,OAAO,UAAU;AAGhE,QAAI,KAAK;AAAO,eAAS,MAAM,SAAS,gBAAgB,SAAS,gBAAgB,SAAS,gBAAgB;AAC1G,aAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAEvC,QAAI,KAAK,YAAY;AACV,eAAA,cAAc,KAAK,gBAAgB,aAAa;AAAA,IAC3D;AAEI,QAAA,KAAK,qBAAqB,QAAW;AACvC,WAAK,MAAM,mBAAmB;AAAA,IAChC;AAEA,aAAS,YAAY;AAAA,EACvB;AACF;"}