{"version": 3, "file": "ParallaxShader.cjs", "sources": ["../../src/shaders/ParallaxShader.ts"], "sourcesContent": ["// Parallax Occlusion shaders from\n//    http://sunandblackcat.com/tipFullView.php?topicid=28\n// No tangent-space transforms logic based on\n//   http://mmikkelsen3d.blogspot.sk/2012/02/parallaxpoc-mapping-and-no-tangent.html\n\nexport const ParallaxShader = {\n  // Ordered from fastest to best quality.\n  modes: {\n    none: 'NO_PARALLAX',\n    basic: 'USE_BASIC_PARALLAX',\n    steep: 'USE_STEEP_PARALLAX',\n    occlusion: 'USE_OCLUSION_PARALLAX', // a.k.a. POM\n    relief: 'USE_RELIEF_PARALLAX',\n  },\n\n  uniforms: {\n    bumpMap: { value: null },\n    map: { value: null },\n    parallaxScale: { value: null },\n    parallaxMinLayers: { value: null },\n    parallaxMaxLayers: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n    varying vec3 vViewPosition;\n    varying vec3 vNormal;\n\n    void main() {\n\n    \tvUv = uv;\n    \tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n    \tvViewPosition = -mvPosition.xyz;\n    \tvNormal = normalize( normalMatrix * normal );\n    \tgl_Position = projectionMatrix * mvPosition;\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D bumpMap;\n    uniform sampler2D map;\n\n    uniform float parallaxScale;\n    uniform float parallaxMinLayers;\n    uniform float parallaxMaxLayers;\n\n    varying vec2 vUv;\n    varying vec3 vViewPosition;\n    varying vec3 vNormal;\n\n    #ifdef USE_BASIC_PARALLAX\n\n    \tvec2 parallaxMap( in vec3 V ) {\n\n    \t\tfloat initialHeight = texture2D( bumpMap, vUv ).r;\n\n    // No Offset Limitting: messy, floating output at grazing angles.\n    //vec2 texCoordOffset = parallaxScale * V.xy / V.z * initialHeight;\n\n    // Offset Limiting\n    \t\tvec2 texCoordOffset = parallaxScale * V.xy * initialHeight;\n    \t\treturn vUv - texCoordOffset;\n\n    \t}\n\n    #else\n\n    \tvec2 parallaxMap( in vec3 V ) {\n\n    // Determine number of layers from angle between V and N\n    \t\tfloat numLayers = mix( parallaxMaxLayers, parallaxMinLayers, abs( dot( vec3( 0.0, 0.0, 1.0 ), V ) ) );\n\n    \t\tfloat layerHeight = 1.0 / numLayers;\n    \t\tfloat currentLayerHeight = 0.0;\n    // Shift of texture coordinates for each iteration\n    \t\tvec2 dtex = parallaxScale * V.xy / V.z / numLayers;\n\n    \t\tvec2 currentTextureCoords = vUv;\n\n    \t\tfloat heightFromTexture = texture2D( bumpMap, currentTextureCoords ).r;\n\n    // while ( heightFromTexture > currentLayerHeight )\n    // Infinite loops are not well supported. Do a \"large\" finite\n    // loop, but not too large, as it slows down some compilers.\n    \t\tfor ( int i = 0; i < 30; i += 1 ) {\n    \t\t\tif ( heightFromTexture <= currentLayerHeight ) {\n    \t\t\t\tbreak;\n    \t\t\t}\n    \t\t\tcurrentLayerHeight += layerHeight;\n    // Shift texture coordinates along vector V\n    \t\t\tcurrentTextureCoords -= dtex;\n    \t\t\theightFromTexture = texture2D( bumpMap, currentTextureCoords ).r;\n    \t\t}\n\n    \t\t#ifdef USE_STEEP_PARALLAX\n\n    \t\t\treturn currentTextureCoords;\n\n    \t\t#elif defined( USE_RELIEF_PARALLAX )\n\n    \t\t\tvec2 deltaTexCoord = dtex / 2.0;\n    \t\t\tfloat deltaHeight = layerHeight / 2.0;\n\n    // Return to the mid point of previous layer\n    \t\t\tcurrentTextureCoords += deltaTexCoord;\n    \t\t\tcurrentLayerHeight -= deltaHeight;\n\n    // Binary search to increase precision of Steep Parallax Mapping\n    \t\t\tconst int numSearches = 5;\n    \t\t\tfor ( int i = 0; i < numSearches; i += 1 ) {\n\n    \t\t\t\tdeltaTexCoord /= 2.0;\n    \t\t\t\tdeltaHeight /= 2.0;\n    \t\t\t\theightFromTexture = texture2D( bumpMap, currentTextureCoords ).r;\n    // Shift along or against vector V\n    \t\t\t\tif( heightFromTexture > currentLayerHeight ) { // Below the surface\n\n    \t\t\t\t\tcurrentTextureCoords -= deltaTexCoord;\n    \t\t\t\t\tcurrentLayerHeight += deltaHeight;\n\n    \t\t\t\t} else { // above the surface\n\n    \t\t\t\t\tcurrentTextureCoords += deltaTexCoord;\n    \t\t\t\t\tcurrentLayerHeight -= deltaHeight;\n\n    \t\t\t\t}\n\n    \t\t\t}\n    \t\t\treturn currentTextureCoords;\n\n    \t\t#elif defined( USE_OCLUSION_PARALLAX )\n\n    \t\t\tvec2 prevTCoords = currentTextureCoords + dtex;\n\n    // Heights for linear interpolation\n    \t\t\tfloat nextH = heightFromTexture - currentLayerHeight;\n    \t\t\tfloat prevH = texture2D( bumpMap, prevTCoords ).r - currentLayerHeight + layerHeight;\n\n    // Proportions for linear interpolation\n    \t\t\tfloat weight = nextH / ( nextH - prevH );\n\n    // Interpolation of texture coordinates\n    \t\t\treturn prevTCoords * weight + currentTextureCoords * ( 1.0 - weight );\n\n    \t\t#else // NO_PARALLAX\n\n    \t\t\treturn vUv;\n\n    \t\t#endif\n\n    \t}\n    #endif\n\n    vec2 perturbUv( vec3 surfPosition, vec3 surfNormal, vec3 viewPosition ) {\n\n    \tvec2 texDx = dFdx( vUv );\n    \tvec2 texDy = dFdy( vUv );\n\n    \tvec3 vSigmaX = dFdx( surfPosition );\n    \tvec3 vSigmaY = dFdy( surfPosition );\n    \tvec3 vR1 = cross( vSigmaY, surfNormal );\n    \tvec3 vR2 = cross( surfNormal, vSigmaX );\n    \tfloat fDet = dot( vSigmaX, vR1 );\n\n    \tvec2 vProjVscr = ( 1.0 / fDet ) * vec2( dot( vR1, viewPosition ), dot( vR2, viewPosition ) );\n    \tvec3 vProjVtex;\n    \tvProjVtex.xy = texDx * vProjVscr.x + texDy * vProjVscr.y;\n    \tvProjVtex.z = dot( surfNormal, viewPosition );\n\n    \treturn parallaxMap( vProjVtex );\n    }\n\n    void main() {\n\n    \tvec2 mapUv = perturbUv( -vViewPosition, normalize( vNormal ), normalize( vViewPosition ) );\n    \tgl_FragColor = texture2D( map, mapUv );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAKO,MAAM,iBAAiB;AAAA;AAAA,EAE5B,OAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA;AAAA,IACX,QAAQ;AAAA,EACV;AAAA,EAEA,UAAU;AAAA,IACR,SAAS,EAAE,OAAO,KAAK;AAAA,IACvB,KAAK,EAAE,OAAO,KAAK;AAAA,IACnB,eAAe,EAAE,OAAO,KAAK;AAAA,IAC7B,mBAAmB,EAAE,OAAO,KAAK;AAAA,IACjC,mBAAmB,EAAE,OAAO,KAAK;AAAA,EACnC;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6I7B;;"}