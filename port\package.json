{"name": "port", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@react-three/drei": "^10.7.6", "@react-three/fiber": "^9.3.0", "@tailwindcss/vite": "^4.1.13", "maath": "^0.10.8", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.9.1", "react-tilt": "^1.0.2", "react-vertical-timeline-component": "^3.5.3", "three": "^0.180.0", "vanta": "^0.5.24"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}