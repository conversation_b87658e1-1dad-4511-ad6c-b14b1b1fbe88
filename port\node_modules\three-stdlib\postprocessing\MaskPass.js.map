{"version": 3, "file": "MaskPass.js", "sources": ["../../src/postprocessing/MaskPass.ts"], "sourcesContent": ["import { Camera, Scene, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { Pass } from './Pass'\n\nclass MaskPass extends Pass {\n  public scene: Scene\n  public camera: Camera\n  public inverse: boolean\n\n  constructor(scene: Scene, camera: Camera) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.clear = true\n    this.needsSwap = false\n\n    this.inverse = false\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    const context = renderer.getContext()\n    const state = renderer.state\n\n    // don't update color or depth\n\n    state.buffers.color.setMask(false)\n    state.buffers.depth.setMask(false)\n\n    // lock buffers\n\n    state.buffers.color.setLocked(true)\n    state.buffers.depth.setLocked(true)\n\n    // set up stencil\n\n    let writeValue, clearValue\n\n    if (this.inverse) {\n      writeValue = 0\n      clearValue = 1\n    } else {\n      writeValue = 1\n      clearValue = 0\n    }\n\n    state.buffers.stencil.setTest(true)\n    state.buffers.stencil.setOp(context.REPLACE, context.REPLACE, context.REPLACE)\n    state.buffers.stencil.setFunc(context.ALWAYS, writeValue, 0xffffffff)\n    state.buffers.stencil.setClear(clearValue)\n    state.buffers.stencil.setLocked(true)\n\n    // draw into the stencil buffer\n\n    renderer.setRenderTarget(readBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.scene, this.camera)\n\n    renderer.setRenderTarget(writeBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.scene, this.camera)\n\n    // unlock color and depth buffer for subsequent rendering\n\n    state.buffers.color.setLocked(false)\n    state.buffers.depth.setLocked(false)\n\n    // only render where stencil is set to 1\n\n    state.buffers.stencil.setLocked(false)\n    state.buffers.stencil.setFunc(context.EQUAL, 1, 0xffffffff) // draw if == 1\n    state.buffers.stencil.setOp(context.KEEP, context.KEEP, context.KEEP)\n    state.buffers.stencil.setLocked(true)\n  }\n}\n\nclass ClearMaskPass extends Pass {\n  constructor() {\n    super()\n    this.needsSwap = false\n  }\n\n  public render(renderer: WebGLRenderer /*, writeBuffer, readBuffer, deltaTime, maskActive */): void {\n    renderer.state.buffers.stencil.setLocked(false)\n    renderer.state.buffers.stencil.setTest(false)\n  }\n}\n\nexport { MaskPass, ClearMaskPass }\n"], "names": [], "mappings": ";;;;;;;AAGA,MAAM,iBAAiB,KAAK;AAAA,EAK1B,YAAY,OAAc,QAAgB;AAClC;AALD;AACA;AACA;AAKL,SAAK,QAAQ;AACb,SAAK,SAAS;AAEd,SAAK,QAAQ;AACb,SAAK,YAAY;AAEjB,SAAK,UAAU;AAAA,EACjB;AAAA,EAEO,OACL,UACA,aACA,YACM;AACA,UAAA,UAAU,SAAS;AACzB,UAAM,QAAQ,SAAS;AAIjB,UAAA,QAAQ,MAAM,QAAQ,KAAK;AAC3B,UAAA,QAAQ,MAAM,QAAQ,KAAK;AAI3B,UAAA,QAAQ,MAAM,UAAU,IAAI;AAC5B,UAAA,QAAQ,MAAM,UAAU,IAAI;AAIlC,QAAI,YAAY;AAEhB,QAAI,KAAK,SAAS;AACH,mBAAA;AACA,mBAAA;AAAA,IAAA,OACR;AACQ,mBAAA;AACA,mBAAA;AAAA,IACf;AAEM,UAAA,QAAQ,QAAQ,QAAQ,IAAI;AAC5B,UAAA,QAAQ,QAAQ,MAAM,QAAQ,SAAS,QAAQ,SAAS,QAAQ,OAAO;AAC7E,UAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,YAAY,UAAU;AAC9D,UAAA,QAAQ,QAAQ,SAAS,UAAU;AACnC,UAAA,QAAQ,QAAQ,UAAU,IAAI;AAIpC,aAAS,gBAAgB,UAAU;AACnC,QAAI,KAAK;AAAO,eAAS,MAAM;AAC/B,aAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAEvC,aAAS,gBAAgB,WAAW;AACpC,QAAI,KAAK;AAAO,eAAS,MAAM;AAC/B,aAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAIjC,UAAA,QAAQ,MAAM,UAAU,KAAK;AAC7B,UAAA,QAAQ,MAAM,UAAU,KAAK;AAI7B,UAAA,QAAQ,QAAQ,UAAU,KAAK;AACrC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,GAAG,UAAU;AACpD,UAAA,QAAQ,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,IAAI;AAC9D,UAAA,QAAQ,QAAQ,UAAU,IAAI;AAAA,EACtC;AACF;AAEA,MAAM,sBAAsB,KAAK;AAAA,EAC/B,cAAc;AACN;AACN,SAAK,YAAY;AAAA,EACnB;AAAA,EAEO,OAAO,UAAqF;AACjG,aAAS,MAAM,QAAQ,QAAQ,UAAU,KAAK;AAC9C,aAAS,MAAM,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EAC9C;AACF;"}