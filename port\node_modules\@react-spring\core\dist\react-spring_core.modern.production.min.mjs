import{each as ge,useIsomorphicLayoutEffect as sn}from"@react-spring/shared";import{is as K,toArray as Wt,eachProp as mt,getFluidValue as $t,isAnimatedString as Zt,Globals as en}from"@react-spring/shared";function I(t,...n){return K.fun(t)?t(...n):t}var ee=(t,n)=>t===!0||!!(n&&t&&(K.fun(t)?t(n):Wt(t).includes(n))),et=(t,n)=>K.obj(t)?n&&t[n]:t;var Ue=(t,n)=>t.default===!0?t[n]:t.default?t.default[n]:void 0,tn=t=>t,te=(t,n=tn)=>{let e=nn;t.default&&t.default!==!0&&(t=t.default,e=Object.keys(t));let r={};for(let o of e){let s=n(t[o],o);K.und(s)||(r[o]=s)}return r},nn=["config","onProps","onStart","onChange","onPause","onResume","onRest"],rn={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function on(t){let n={},e=0;if(mt(t,(r,o)=>{rn[o]||(n[o]=r,e++)}),e)return n}function de(t){let n=on(t);if(n){let e={to:n};return mt(t,(r,o)=>o in n||(e[o]=r)),e}return{...t}}function me(t){return t=$t(t),K.arr(t)?t.map(me):Zt(t)?en.createStringInterpolator({range:[0,1],output:[t,t]})(1):t}function Ee(t){for(let n in t)return!0;return!1}function Le(t){return K.fun(t)||K.arr(t)&&K.obj(t[0])}function xe(t,n){t.ref?.delete(t),n?.delete(t)}function he(t,n){n&&t.ref!==n&&(t.ref?.delete(t),n.add(t),t.ref=n)}function Fr(t,n,e=1e3){sn(()=>{if(n){let r=0;ge(t,(o,s)=>{let a=o.current;if(a.length){let i=e*n[s];isNaN(i)?i=r:r=i,ge(a,p=>{ge(p.queue,u=>{let b=u.delay;u.delay=l=>i+I(b||0,l)})}),o.start()}})}else{let r=Promise.resolve();ge(t,o=>{let s=o.current;if(s.length){let a=s.map(i=>{let p=i.queue;return i.queue=[],p});r=r.then(()=>(ge(s,(i,p)=>ge(a[p]||[],u=>i.queue.push(u))),Promise.all(o.start())))}})}})}import{is as jn}from"@react-spring/shared";import{useContext as Un,useMemo as Ke,useRef as lt}from"react";import{is as En,each as Je,usePrev as Nt,useOnce as Ln,useForceUpdate as wn,useIsomorphicLayoutEffect as Mn}from"@react-spring/shared";import{is as R,raf as ve,each as Rt,isEqual as Y,toArray as vt,eachProp as Sn,frameLoop as Pn,flushCalls as ze,getFluidValue as se,isAnimatedString as Tn,Globals as xn,callFluidObservers as bn,hasFluidValue as Se,addFluidObserver as An,removeFluidObserver as Rn,getFluidObservers as Ct}from"@react-spring/shared";import{AnimatedValue as vn,AnimatedString as It,getPayload as Cn,getAnimated as ie,setAnimated as In,getAnimatedType as Vt}from"@react-spring/animated";import{is as ne,easings as an}from"@react-spring/shared";var ht={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}};var tt={...ht.default,mass:1,damping:1,easing:an.linear,clamp:!1},we=class{constructor(){this.velocity=0;Object.assign(this,tt)}};function yt(t,n,e){e&&(e={...e},gt(e,n),n={...e,...n}),gt(t,n),Object.assign(t,n);for(let a in tt)t[a]==null&&(t[a]=tt[a]);let{frequency:r,damping:o}=t,{mass:s}=t;return ne.und(r)||(r<.01&&(r=.01),o<0&&(o=0),t.tension=Math.pow(2*Math.PI/r,2)*s,t.friction=4*Math.PI*o*s/r),t}function gt(t,n){if(!ne.und(n.decay))t.duration=void 0;else{let e=!ne.und(n.tension)||!ne.und(n.friction);(e||!ne.und(n.frequency)||!ne.und(n.damping)||!ne.und(n.mass))&&(t.duration=void 0,t.decay=void 0),e&&(t.frequency=void 0)}}var St=[],Me=class{constructor(){this.changed=!1;this.values=St;this.toValues=null;this.fromValues=St;this.config=new we;this.immediate=!1}};import{is as un,raf as Pt,Globals as pn}from"@react-spring/shared";function je(t,{key:n,props:e,defaultProps:r,state:o,actions:s}){return new Promise((a,i)=>{let p,u,b=ee(e.cancel??r?.cancel,n);if(b)g();else{un.und(e.pause)||(o.paused=ee(e.pause,n));let h=r?.pause;h!==!0&&(h=o.paused||ee(h,n)),p=I(e.delay||0,n),h?(o.resumeQueue.add(y),s.pause()):(s.resume(),y())}function l(){o.resumeQueue.add(y),o.timeouts.delete(u),u.cancel(),p=u.time-Pt.now()}function y(){p>0&&!pn.skipAnimation?(o.delayed=!0,u=Pt.setTimeout(g,p),o.pauseQueue.add(l),o.timeouts.add(u)):g()}function g(){o.delayed&&(o.delayed=!1),o.pauseQueue.delete(l),o.timeouts.delete(u),t<=(o.cancelId||0)&&(b=!0);try{s.start({...e,callId:t,cancel:b},a)}catch(h){i(h)}}})}import{is as De,raf as ln,flush as cn,eachProp as fn,Globals as Tt}from"@react-spring/shared";var be=(t,n)=>n.length==1?n[0]:n.some(e=>e.cancelled)?q(t.get()):n.every(e=>e.noop)?nt(t.get()):L(t.get(),n.every(e=>e.finished)),nt=t=>({value:t,noop:!0,finished:!0,cancelled:!1}),L=(t,n,e=!1)=>({value:t,finished:n,cancelled:e}),q=t=>({value:t,cancelled:!0,finished:!1});function qe(t,n,e,r){let{callId:o,parentId:s,onRest:a}=n,{asyncTo:i,promise:p}=e;return!s&&t===i&&!n.reset?p:e.promise=(async()=>{e.asyncId=o,e.asyncTo=t;let u=te(n,(d,f)=>f==="onRest"?void 0:d),b,l,y=new Promise((d,f)=>(b=d,l=f)),g=d=>{let f=o<=(e.cancelId||0)&&q(r)||o!==e.asyncId&&L(r,!1);if(f)throw d.result=f,l(d),d},h=(d,f)=>{let T=new Ae,S=new Ne;return(async()=>{if(Tt.skipAnimation)throw re(e),S.result=L(r,!1),l(S),S;g(T);let A=De.obj(d)?{...d}:{...f,to:d};A.parentId=o,fn(u,(C,v)=>{De.und(A[v])&&(A[v]=C)});let x=await r.start(A);return g(T),e.paused&&await new Promise(C=>{e.resumeQueue.add(C)}),x})()},c;if(Tt.skipAnimation)return re(e),L(r,!1);try{let d;De.arr(t)?d=(async f=>{for(let T of f)await h(T)})(t):d=Promise.resolve(t(h,r.stop.bind(r))),await Promise.all([d.then(b),y]),c=L(r.get(),!0,!1)}catch(d){if(d instanceof Ae)c=d.result;else if(d instanceof Ne)c=d.result;else throw d}finally{o==e.asyncId&&(e.asyncId=s,e.asyncTo=s?i:void 0,e.promise=s?p:void 0)}return De.fun(a)&&ln.batchedUpdates(()=>{a(c,r,r.item)}),c})()}function re(t,n){cn(t.timeouts,e=>e.cancel()),t.pauseQueue.clear(),t.resumeQueue.clear(),t.asyncId=t.asyncTo=t.promise=void 0,n&&(t.cancelId=n)}var Ae=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},Ne=class extends Error{constructor(){super("SkipAnimationSignal")}};import{deprecateInterpolate as dn,frameLoop as mn,FluidValue as hn,Globals as xt,callFluidObservers as bt}from"@react-spring/shared";import{getAnimated as gn}from"@react-spring/animated";var Re=t=>t instanceof J,yn=1,J=class extends hn{constructor(){super(...arguments);this.id=yn++;this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=gn(this);return e&&e.getValue()}to(...e){return xt.to(this,e)}interpolate(...e){return dn(),xt.to(this,e)}toJSON(){return this.get()}observerAdded(e){e==1&&this._attach()}observerRemoved(e){e==0&&this._detach()}_attach(){}_detach(){}_onChange(e,r=!1){bt(this,{type:"change",parent:this,value:e,idle:r})}_onPriorityChange(e){this.idle||mn.sort(this),bt(this,{type:"priority",parent:this,priority:e})}};var oe=Symbol.for("SpringPhase"),At=1,rt=2,ot=4,Qe=t=>(t[oe]&At)>0,Q=t=>(t[oe]&rt)>0,ye=t=>(t[oe]&ot)>0,st=(t,n)=>n?t[oe]|=rt|At:t[oe]&=~rt,it=(t,n)=>n?t[oe]|=ot:t[oe]&=~ot;var ae=class extends J{constructor(e,r){super();this.animation=new Me;this.defaultProps={};this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._pendingCalls=new Set;this._lastCallId=0;this._lastToId=0;this._memoizedDuration=0;if(!R.und(e)||!R.und(r)){let o=R.obj(e)?{...e}:{...r,from:e};R.und(o.default)&&(o.default=!0),this.start(o)}}get idle(){return!(Q(this)||this._state.asyncTo)||ye(this)}get goal(){return se(this.animation.to)}get velocity(){let e=ie(this);return e instanceof vn?e.lastVelocity||0:e.getPayload().map(r=>r.lastVelocity||0)}get hasAnimated(){return Qe(this)}get isAnimating(){return Q(this)}get isPaused(){return ye(this)}get isDelayed(){return this._state.delayed}advance(e){let r=!0,o=!1,s=this.animation,{toValues:a}=s,{config:i}=s,p=Cn(s.to);!p&&Se(s.to)&&(a=vt(se(s.to))),s.values.forEach((l,y)=>{if(l.done)return;let g=l.constructor==It?1:p?p[y].lastPosition:a[y],h=s.immediate,c=g;if(!h){if(c=l.lastPosition,i.tension<=0){l.done=!0;return}let d=l.elapsedTime+=e,f=s.fromValues[y],T=l.v0!=null?l.v0:l.v0=R.arr(i.velocity)?i.velocity[y]:i.velocity,S,A=i.precision||(f==g?.005:Math.min(1,Math.abs(g-f)*.001));if(R.und(i.duration))if(i.decay){let x=i.decay===!0?.998:i.decay,C=Math.exp(-(1-x)*d);c=f+T/(1-x)*(1-C),h=Math.abs(l.lastPosition-c)<=A,S=T*C}else{S=l.lastVelocity==null?T:l.lastVelocity;let x=i.restVelocity||A/10,C=i.clamp?0:i.bounce,v=!R.und(C),E=f==g?l.v0>0:f<g,V,M=!1,U=1,X=Math.ceil(e/U);for(let w=0;w<X&&(V=Math.abs(S)>x,!(!V&&(h=Math.abs(g-c)<=A,h)));++w){v&&(M=c==g||c>g==E,M&&(S=-S*C,c=g));let m=-i.tension*1e-6*(c-g),P=-i.friction*.001*S,_=(m+P)/i.mass;S=S+_*U,c=c+S*U}}else{let x=1;i.duration>0&&(this._memoizedDuration!==i.duration&&(this._memoizedDuration=i.duration,l.durationProgress>0&&(l.elapsedTime=i.duration*l.durationProgress,d=l.elapsedTime+=e)),x=(i.progress||0)+d/this._memoizedDuration,x=x>1?1:x<0?0:x,l.durationProgress=x),c=f+i.easing(x)*(g-f),S=(c-l.lastPosition)/e,h=x==1}l.lastVelocity=S,Number.isNaN(c)&&(console.warn("Got NaN while animating:",this),h=!0)}p&&!p[y].done&&(h=!1),h?l.done=!0:r=!1,l.setValue(c,i.round)&&(o=!0)});let u=ie(this),b=u.getValue();if(r){let l=se(s.to);(b!==l||o)&&!i.decay?(u.setValue(l),this._onChange(l)):o&&i.decay&&this._onChange(b),this._stop()}else o&&this._onChange(b)}set(e){return ve.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(Q(this)){let{to:e,config:r}=this.animation;ve.batchedUpdates(()=>{this._onStart(),r.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,r){let o;return R.und(e)?(o=this.queue||[],this.queue=[]):o=[R.obj(e)?e:{...r,to:e}],Promise.all(o.map(s=>this._update(s))).then(s=>be(this,s))}stop(e){let{to:r}=this.animation;return this._focus(this.get()),re(this._state,e&&this._lastCallId),ve.batchedUpdates(()=>this._stop(r,e)),this}reset(){this._update({reset:!0})}eventObserved(e){e.type=="change"?this._start():e.type=="priority"&&(this.priority=e.priority+1)}_prepareNode(e){let r=this.key||"",{to:o,from:s}=e;o=R.obj(o)?o[r]:o,(o==null||Le(o))&&(o=void 0),s=R.obj(s)?s[r]:s,s==null&&(s=void 0);let a={to:o,from:s};return Qe(this)||(e.reverse&&([o,s]=[s,o]),s=se(s),R.und(s)?ie(this)||this._set(o):this._set(s)),a}_update({...e},r){let{key:o,defaultProps:s}=this;e.default&&Object.assign(s,te(e,(p,u)=>/^on/.test(u)?et(p,o):p)),Ot(this,e,"onProps"),Ie(this,"onProps",e,this);let a=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let i=this._state;return je(++this._lastCallId,{key:o,props:e,defaultProps:s,state:i,actions:{pause:()=>{ye(this)||(it(this,!0),ze(i.pauseQueue),Ie(this,"onPause",L(this,Ce(this,this.animation.to)),this))},resume:()=>{ye(this)&&(it(this,!1),Q(this)&&this._resume(),ze(i.resumeQueue),Ie(this,"onResume",L(this,Ce(this,this.animation.to)),this))},start:this._merge.bind(this,a)}}).then(p=>{if(e.loop&&p.finished&&!(r&&p.noop)){let u=at(e);if(u)return this._update(u,!0)}return p})}_merge(e,r,o){if(r.cancel)return this.stop(!0),o(q(this));let s=!R.und(e.to),a=!R.und(e.from);if(s||a)if(r.callId>this._lastToId)this._lastToId=r.callId;else return o(q(this));let{key:i,defaultProps:p,animation:u}=this,{to:b,from:l}=u,{to:y=b,from:g=l}=e;a&&!s&&(!r.default||R.und(y))&&(y=g),r.reverse&&([y,g]=[g,y]);let h=!Y(g,l);h&&(u.from=g),g=se(g);let c=!Y(y,b);c&&this._focus(y);let d=Le(r.to),{config:f}=u,{decay:T,velocity:S}=f;(s||a)&&(f.velocity=0),r.config&&!d&&yt(f,I(r.config,i),r.config!==p.config?I(p.config,i):void 0);let A=ie(this);if(!A||R.und(y))return o(L(this,!0));let x=R.und(r.reset)?a&&!r.default:!R.und(g)&&ee(r.reset,i),C=x?g:this.get(),v=me(y),E=R.num(v)||R.arr(v)||Tn(v),V=!d&&(!E||ee(p.immediate||r.immediate,i));if(c){let w=Vt(y);if(w!==A.constructor)if(V)A=this._set(v);else throw Error(`Cannot animate between ${A.constructor.name} and ${w.name}, as the "to" prop suggests`)}let M=A.constructor,U=Se(y),X=!1;if(!U){let w=x||!Qe(this)&&h;(c||w)&&(X=Y(me(C),v),U=!X),(!Y(u.immediate,V)&&!V||!Y(f.decay,T)||!Y(f.velocity,S))&&(U=!0)}if(X&&Q(this)&&(u.changed&&!x?U=!0:U||this._stop(b)),!d&&((U||Se(b))&&(u.values=A.getPayload(),u.toValues=Se(y)?null:M==It?[1]:vt(v)),u.immediate!=V&&(u.immediate=V,!V&&!x&&this._set(b)),U)){let{onRest:w}=u;Rt(Vn,P=>Ot(this,r,P));let m=L(this,Ce(this,b));ze(this._pendingCalls,m),this._pendingCalls.add(o),u.changed&&ve.batchedUpdates(()=>{u.changed=!x,w?.(m,this),x?I(p.onRest,m):u.onStart?.(m,this)})}x&&this._set(C),d?o(qe(r.to,r,this._state,this)):U?this._start():Q(this)&&!c?this._pendingCalls.add(o):o(nt(C))}_focus(e){let r=this.animation;e!==r.to&&(Ct(this)&&this._detach(),r.to=e,Ct(this)&&this._attach())}_attach(){let e=0,{to:r}=this.animation;Se(r)&&(An(r,this),Re(r)&&(e=r.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;Se(e)&&Rn(e,this)}_set(e,r=!0){let o=se(e);if(!R.und(o)){let s=ie(this);if(!s||!Y(o,s.getValue())){let a=Vt(o);!s||s.constructor!=a?In(this,a.create(o)):s.setValue(o),s&&ve.batchedUpdates(()=>{this._onChange(o,r)})}}return ie(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,Ie(this,"onStart",L(this,Ce(this,e.to)),this))}_onChange(e,r){r||(this._onStart(),I(this.animation.onChange,e,this)),I(this.defaultProps.onChange,e,this),super._onChange(e,r)}_start(){let e=this.animation;ie(this).reset(se(e.to)),e.immediate||(e.fromValues=e.values.map(r=>r.lastPosition)),Q(this)||(st(this,!0),ye(this)||this._resume())}_resume(){xn.skipAnimation?this.finish():Pn.start(this)}_stop(e,r){if(Q(this)){st(this,!1);let o=this.animation;Rt(o.values,a=>{a.done=!0}),o.toValues&&(o.onChange=o.onPause=o.onResume=void 0),bn(this,{type:"idle",parent:this});let s=r?q(this.get()):L(this.get(),Ce(this,e??o.to));ze(this._pendingCalls,s),o.changed&&(o.changed=!1,Ie(this,"onRest",s,this))}}};function Ce(t,n){let e=me(n),r=me(t.get());return Y(r,e)}function at(t,n=t.loop,e=t.to){let r=I(n);if(r){let o=r!==!0&&de(r),s=(o||t).reverse,a=!o||o.reset;return Pe({...t,loop:n,default:!1,pause:void 0,to:!s||Le(e)?e:void 0,from:a?t.from:void 0,reset:a,...o})}}function Pe(t){let{to:n,from:e}=t=de(t),r=new Set;return R.obj(n)&&_t(n,r),R.obj(e)&&_t(e,r),t.keys=r.size?Array.from(r):null,t}function Ft(t){let n=Pe(t);return R.und(n.default)&&(n.default=te(n)),n}function _t(t,n){Sn(t,(e,r)=>e!=null&&n.add(r))}var Vn=["onStart","onRest","onChange","onPause","onResume"];function Ot(t,n,e){t.animation[e]=n[e]!==Ue(n,e)?et(n[e],t.key):void 0}function Ie(t,n,...e){t.animation[n]?.(...e),t.defaultProps[n]?.(...e)}import{is as z,raf as Ut,each as ue,noop as kt,flush as ut,toArray as Ve,eachProp as Et,flushCalls as _n,addFluidObserver as Lt}from"@react-spring/shared";var On=["onStart","onChange","onRest"],Fn=1,pe=class{constructor(n,e){this.id=Fn++;this.springs={};this.queue=[];this._lastAsyncId=0;this._active=new Set;this._changed=new Set;this._started=!1;this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._events={onStart:new Map,onChange:new Map,onRest:new Map};this._onFrame=this._onFrame.bind(this),e&&(this._flush=e),n&&this.start({default:!0,...n})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(n=>n.idle&&!n.isDelayed&&!n.isPaused)}get item(){return this._item}set item(n){this._item=n}get(){let n={};return this.each((e,r)=>n[r]=e.get()),n}set(n){for(let e in n){let r=n[e];z.und(r)||this.springs[e].set(r)}}update(n){return n&&this.queue.push(Pe(n)),this}start(n){let{queue:e}=this;return n?e=Ve(n).map(Pe):this.queue=[],this._flush?this._flush(this,e):(Dt(this,e),Ge(this,e))}stop(n,e){if(n!==!!n&&(e=n),e){let r=this.springs;ue(Ve(e),o=>r[o].stop(!!n))}else re(this._state,this._lastAsyncId),this.each(r=>r.stop(!!n));return this}pause(n){if(z.und(n))this.start({pause:!0});else{let e=this.springs;ue(Ve(n),r=>e[r].pause())}return this}resume(n){if(z.und(n))this.start({pause:!1});else{let e=this.springs;ue(Ve(n),r=>e[r].resume())}return this}each(n){Et(this.springs,n)}_onFrame(){let{onStart:n,onChange:e,onRest:r}=this._events,o=this._active.size>0,s=this._changed.size>0;(o&&!this._started||s&&!this._started)&&(this._started=!0,ut(n,([p,u])=>{u.value=this.get(),p(u,this,this._item)}));let a=!o&&this._started,i=s||a&&r.size?this.get():null;s&&e.size&&ut(e,([p,u])=>{u.value=i,p(u,this,this._item)}),a&&(this._started=!1,ut(r,([p,u])=>{u.value=i,p(u,this,this._item)}))}eventObserved(n){if(n.type=="change")this._changed.add(n.parent),n.idle||this._active.add(n.parent);else if(n.type=="idle")this._active.delete(n.parent);else return;Ut.onFrame(this._onFrame)}};function Ge(t,n){return Promise.all(n.map(e=>wt(t,e))).then(e=>be(t,e))}async function wt(t,n,e){let{keys:r,to:o,from:s,loop:a,onRest:i,onResolve:p}=n,u=z.obj(n.default)&&n.default;a&&(n.loop=!1),o===!1&&(n.to=null),s===!1&&(n.from=null);let b=z.arr(o)||z.fun(o)?o:void 0;b?(n.to=void 0,n.onRest=void 0,u&&(u.onRest=void 0)):ue(On,c=>{let d=n[c];if(z.fun(d)){let f=t._events[c];n[c]=({finished:T,cancelled:S})=>{let A=f.get(d);A?(T||(A.finished=!1),S&&(A.cancelled=!0)):f.set(d,{value:null,finished:T||!1,cancelled:S||!1})},u&&(u[c]=n[c])}});let l=t._state;n.pause===!l.paused?(l.paused=n.pause,_n(n.pause?l.pauseQueue:l.resumeQueue)):l.paused&&(n.pause=!0);let y=(r||Object.keys(t.springs)).map(c=>t.springs[c].start(n)),g=n.cancel===!0||Ue(n,"cancel")===!0;(b||g&&l.asyncId)&&y.push(je(++t._lastAsyncId,{props:n,state:l,actions:{pause:kt,resume:kt,start(c,d){g?(re(l,t._lastAsyncId),d(q(t))):(c.onRest=i,d(qe(b,c,l,t)))}}})),l.paused&&await new Promise(c=>{l.resumeQueue.add(c)});let h=be(t,await Promise.all(y));if(a&&h.finished&&!(e&&h.noop)){let c=at(n,a,o);if(c)return Dt(t,[c]),wt(t,c,!0)}return p&&Ut.batchedUpdates(()=>p(h,t,t.item)),h}function _e(t,n){let e={...t.springs};return n&&ue(Ve(n),r=>{z.und(r.keys)&&(r=Pe(r)),z.obj(r.to)||(r={...r,to:void 0}),jt(e,r,o=>Mt(o))}),pt(t,e),e}function pt(t,n){Et(n,(e,r)=>{t.springs[r]||(t.springs[r]=e,Lt(e,t))})}function Mt(t,n){let e=new ae;return e.key=t,n&&Lt(e,n),e}function jt(t,n,e){n.keys&&ue(n.keys,r=>{(t[r]||(t[r]=e(r)))._prepareNode(n)})}function Dt(t,n){ue(n,e=>{jt(t.springs,e,r=>Mt(r,t))})}import*as Xe from"react";import{useContext as vo}from"react";var Oe=Xe.createContext({pause:!1,immediate:!1});import{each as le,is as Be,deprecateDirectCall as kn}from"@react-spring/shared";var ce=()=>{let t=[],n=function(r){kn();let o=[];return le(t,(s,a)=>{if(Be.und(r))o.push(s.start());else{let i=e(r,s,a);i&&o.push(s.start(i))}}),o};n.current=t,n.add=function(r){t.includes(r)||t.push(r)},n.delete=function(r){let o=t.indexOf(r);~o&&t.splice(o,1)},n.pause=function(){return le(t,r=>r.pause(...arguments)),this},n.resume=function(){return le(t,r=>r.resume(...arguments)),this},n.set=function(r){le(t,(o,s)=>{let a=Be.fun(r)?r(s,o):r;a&&o.set(a)})},n.start=function(r){let o=[];return le(t,(s,a)=>{if(Be.und(r))o.push(s.start());else{let i=this._getProps(r,s,a);i&&o.push(s.start(i))}}),o},n.stop=function(){return le(t,r=>r.stop(...arguments)),this},n.update=function(r){return le(t,(o,s)=>o.update(this._getProps(r,o,s))),this};let e=function(r,o,s){return Be.fun(r)?r(s,o):r};return n._getProps=e,n};function Ye(t,n,e){let r=En.fun(n)&&n;r&&!e&&(e=[]);let o=Ke(()=>r||arguments.length==3?ce():void 0,[]),s=lt(0),a=wn(),i=Ke(()=>({ctrls:[],queue:[],flush(f,T){let S=_e(f,T);return s.current>0&&!i.queue.length&&!Object.keys(S).some(x=>!f.springs[x])?Ge(f,T):new Promise(x=>{pt(f,S),i.queue.push(()=>{x(Ge(f,T))}),a()})}}),[]),p=lt([...i.ctrls]),u=lt([]),b=Nt(t)||0;Ke(()=>{Je(p.current.slice(t,b),f=>{xe(f,o),f.stop(!0)}),p.current.length=t,l(b,t)},[t]),Ke(()=>{l(0,Math.min(b,t))},e);function l(f,T){for(let S=f;S<T;S++){let A=p.current[S]||(p.current[S]=new pe(null,i.flush)),x=r?r(S,A):n[S];x&&(u.current[S]=Ft(x))}}let y=p.current.map((f,T)=>_e(f,u.current[T])),g=Un(Oe),h=Nt(g),c=g!==h&&Ee(g);Mn(()=>{s.current++,i.ctrls=p.current;let{queue:f}=i;f.length&&(i.queue=[],Je(f,T=>T())),Je(p.current,(T,S)=>{o?.add(T),c&&T.start({default:g});let A=u.current[S];A&&(he(T,A.ref),T.ref?T.queue.push(A):T.start(A))})}),Ln(()=>()=>{Je(i.ctrls,f=>f.stop(!0))});let d=y.map(f=>({...f}));return o?[d,o]:d}function H(t,n){let e=jn.fun(t),[[r],o]=Ye(1,e?t:[t],e?n||[]:n);return e||arguments.length==2?[r,o]:r}import{useState as Dn}from"react";var Nn=()=>ce(),Go=()=>Dn(Nn)[0];import{useConstant as qn,useOnce as Qn}from"@react-spring/shared";var Jo=(t,n)=>{let e=qn(()=>new ae(t,n));return Qn(()=>()=>{e.stop()}),e};import{each as zn,is as qt,useIsomorphicLayoutEffect as Gn}from"@react-spring/shared";function Qt(t,n,e){let r=qt.fun(n)&&n;r&&!e&&(e=[]);let o=!0,s,a=Ye(t,(i,p)=>{let u=r?r(i,p):n;return s=u.ref,o=o&&u.reverse,u},e||[{}]);if(Gn(()=>{zn(a[1].current,(i,p)=>{let u=a[1].current[p+(o?1:-1)];if(he(i,s),i.ref){u&&i.update({to:u.springs});return}u?i.start({to:u.springs}):i.start()})},e),r||arguments.length==3){let i=s??a[1];return i._getProps=(p,u,b)=>{let l=qt.fun(p)?p(b,u):p;if(l){let y=i.current[b+(l.reverse?1:-1)];return y&&(l.to=y.springs),l}},a}return a[0]}import*as fe from"react";import{useContext as Xn,useRef as ct,useMemo as Bn}from"react";import{is as G,toArray as zt,useForceUpdate as Kn,useOnce as Jn,usePrev as Yn,each as N,useIsomorphicLayoutEffect as He}from"@react-spring/shared";function Gt(t,n,e){let r=G.fun(n)&&n,{reset:o,sort:s,trail:a=0,expires:i=!0,exitBeforeEnter:p=!1,onDestroyed:u,ref:b,config:l}=r?r():n,y=Bn(()=>r||arguments.length==3?ce():void 0,[]),g=zt(t),h=[],c=ct(null),d=o?null:c.current;He(()=>{c.current=h}),Jn(()=>(N(h,m=>{y?.add(m.ctrl),m.ctrl.ref=y}),()=>{N(c.current,m=>{m.expired&&clearTimeout(m.expirationId),xe(m.ctrl,y),m.ctrl.stop(!0)})}));let f=Wn(g,r?r():n,d),T=o&&c.current||[];He(()=>N(T,({ctrl:m,item:P,key:_})=>{xe(m,y),I(u,P,_)}));let S=[];if(d&&N(d,(m,P)=>{m.expired?(clearTimeout(m.expirationId),T.push(m)):(P=S[P]=f.indexOf(m.key),~P&&(h[P]=m))}),N(g,(m,P)=>{h[P]||(h[P]={key:f[P],item:m,phase:"mount",ctrl:new pe},h[P].ctrl.item=m)}),S.length){let m=-1,{leave:P}=r?r():n;N(S,(_,F)=>{let O=d[F];~_?(m=h.indexOf(O),h[m]={...O,item:g[_]}):P&&h.splice(++m,0,O)})}G.fun(s)&&h.sort((m,P)=>s(m.item,P.item));let A=-a,x=Kn(),C=te(n),v=new Map,E=ct(new Map),V=ct(!1);N(h,(m,P)=>{let _=m.key,F=m.phase,O=r?r():n,k,j,Fe=I(O.delay||0,_);if(F=="mount")k=O.enter,j="enter";else{let D=f.indexOf(_)<0;if(F!="leave")if(D)k=O.leave,j="leave";else if(k=O.update)j="update";else return;else if(!D)k=O.enter,j="enter";else return}if(k=I(k,m.item,P),k=G.obj(k)?de(k):{to:k},!k.config){let D=l||C.config;k.config=I(D,m.item,P,j)}A+=a;let $={...C,delay:Fe+A,ref:b,immediate:O.immediate,reset:!1,...k};if(j=="enter"&&G.und($.from)){let D=r?r():n,Te=G.und(D.initial)||d?D.from:D.initial;$.from=I(Te,m.item,P)}let{onResolve:Ht}=$;$.onResolve=D=>{I(Ht,D);let Te=c.current,B=Te.find(ke=>ke.key===_);if(B&&!(D.cancelled&&B.phase!="update")&&B.ctrl.idle){let ke=Te.every(Z=>Z.ctrl.idle);if(B.phase=="leave"){let Z=I(i,B.item);if(Z!==!1){let Ze=Z===!0?0:Z;if(B.expired=!0,!ke&&Ze>0){Ze<=2147483647&&(B.expirationId=setTimeout(x,Ze));return}}}ke&&Te.some(Z=>Z.expired)&&(E.current.delete(B),p&&(V.current=!0),x())}};let dt=_e(m.ctrl,$);j==="leave"&&p?E.current.set(m,{phase:j,springs:dt,payload:$}):v.set(m,{phase:j,springs:dt,payload:$})});let M=Xn(Oe),U=Yn(M),X=M!==U&&Ee(M);He(()=>{X&&N(h,m=>{m.ctrl.start({default:M})})},[M]),N(v,(m,P)=>{if(E.current.size){let _=h.findIndex(F=>F.key===P.key);h.splice(_,1)}}),He(()=>{N(E.current.size?E.current:v,({phase:m,payload:P},_)=>{let{ctrl:F}=_;_.phase=m,y?.add(F),X&&m=="enter"&&F.start({default:M}),P&&(he(F,P.ref),(F.ref||y)&&!V.current?F.update(P):(F.start(P),V.current&&(V.current=!1)))})},o?void 0:e);let w=m=>fe.createElement(fe.Fragment,null,h.map((P,_)=>{let{springs:F}=v.get(P)||P.ctrl,O=m({...F},P.item,P,_),k=G.str(P.key)||G.num(P.key)?P.key:P.ctrl.id,j=fe.version<"19.0.0",Fe=O?.props??{};return j&&(Fe.ref=O.ref),O&&O.type?fe.createElement(O.type,{key:k,...Fe}):O}));return y?[w,y]:w}var Hn=1;function Wn(t,{key:n,keys:e=n},r){if(e===null){let o=new Set;return t.map(s=>{let a=r&&r.find(i=>i.item===s&&i.phase!=="leave"&&!o.has(i));return a?(o.add(a),a.key):Hn++})}return G.und(e)?t:G.fun(e)?t.map(e):zt(e)}import{each as $n,onScroll as Zn,useIsomorphicLayoutEffect as er}from"@react-spring/shared";var fs=({container:t,...n}={})=>{let[e,r]=H(()=>({scrollX:0,scrollY:0,scrollXProgress:0,scrollYProgress:0,...n}),[]);return er(()=>{let o=Zn(({x:s,y:a})=>{r.start({scrollX:s.current,scrollXProgress:s.progress,scrollY:a.current,scrollYProgress:a.progress})},{container:t?.current||void 0});return()=>{$n(Object.values(e),s=>s.stop()),o()}},[]),e};import{onResize as tr,each as nr,useIsomorphicLayoutEffect as rr}from"@react-spring/shared";var gs=({container:t,...n})=>{let[e,r]=H(()=>({width:0,height:0,...n}),[]);return rr(()=>{let o=tr(({width:s,height:a})=>{r.start({width:s,height:a,immediate:e.width.get()===0||e.height.get()===0||n.immediate===!0})},{container:t?.current||void 0});return()=>{nr(Object.values(e),s=>s.stop()),o()}},[]),e};import{useRef as or,useState as sr}from"react";import{is as Xt,useIsomorphicLayoutEffect as ir}from"@react-spring/shared";var ar={any:0,all:1};function As(t,n){let[e,r]=sr(!1),o=or(void 0),s=Xt.fun(t)&&t,a=s?s():{},{to:i={},from:p={},...u}=a,b=s?n:t,[l,y]=H(()=>({from:p,...u}),[]);return ir(()=>{let g=o.current,{root:h,once:c,amount:d="any",...f}=b??{};if(!g||c&&e||typeof IntersectionObserver>"u")return;let T=new WeakMap,S=()=>(i&&y.start(i),r(!0),c?void 0:()=>{p&&y.start(p),r(!1)}),A=C=>{C.forEach(v=>{let E=T.get(v.target);if(v.isIntersecting!==!!E)if(v.isIntersecting){let V=S();Xt.fun(V)?T.set(v.target,V):x.unobserve(v.target)}else E&&(E(),T.delete(v.target))})},x=new IntersectionObserver(A,{root:h&&h.current||void 0,threshold:typeof d=="number"||Array.isArray(d)?d:ar[d],...f});return x.observe(g),()=>x.unobserve(g)},[b]),s?[o,l]:[o,e]}function js({children:t,...n}){return t(H(n))}import{is as ur}from"@react-spring/shared";function Qs({items:t,children:n,...e}){let r=Qt(t.length,e);return t.map((o,s)=>{let a=n(o,s);return ur.fun(a)?a(r[s]):a})}function Xs({items:t,children:n,...e}){return Gt(t,e)(n)}import{deprecateInterpolate as br}from"@react-spring/shared";import{is as pr,raf as lr,each as $e,isEqual as cr,toArray as We,frameLoop as fr,getFluidValue as Bt,createInterpolator as dr,Globals as mr,callFluidObservers as hr,addFluidObserver as gr,removeFluidObserver as yr,hasFluidValue as Kt}from"@react-spring/shared";import{getAnimated as Sr,setAnimated as Pr,getAnimatedType as Tr,getPayload as Yt}from"@react-spring/animated";var W=class extends J{constructor(e,r){super();this.source=e;this.idle=!0;this._active=new Set;this.calc=dr(...r);let o=this._get(),s=Tr(o);Pr(this,s.create(o))}advance(e){let r=this._get(),o=this.get();cr(r,o)||(Sr(this).setValue(r),this._onChange(r,this.idle)),!this.idle&&Jt(this._active)&&ft(this)}_get(){let e=pr.arr(this.source)?this.source.map(Bt):We(Bt(this.source));return this.calc(...e)}_start(){this.idle&&!Jt(this._active)&&(this.idle=!1,$e(Yt(this),e=>{e.done=!1}),mr.skipAnimation?(lr.batchedUpdates(()=>this.advance()),ft(this)):fr.start(this))}_attach(){let e=1;$e(We(this.source),r=>{Kt(r)&&gr(r,this),Re(r)&&(r.idle||this._active.add(r),e=Math.max(e,r.priority+1))}),this.priority=e,this._start()}_detach(){$e(We(this.source),e=>{Kt(e)&&yr(e,this)}),this._active.clear(),ft(this)}eventObserved(e){e.type=="change"?e.idle?this.advance():(this._active.add(e.parent),this._start()):e.type=="idle"?this._active.delete(e.parent):e.type=="priority"&&(this.priority=We(this.source).reduce((r,o)=>Math.max(r,(Re(o)?o.priority:0)+1),0))}};function xr(t){return t.idle!==!1}function Jt(t){return!t.size||Array.from(t).every(xr)}function ft(t){t.idle||(t.idle=!0,$e(Yt(t),n=>{n.done=!0}),hr(t,{type:"idle",parent:t}))}var si=(t,...n)=>new W(t,n),ii=(t,...n)=>(br(),new W(t,n));import{Globals as Ar,frameLoop as Rr,createStringInterpolator as vr}from"@react-spring/shared";Ar.assign({createStringInterpolator:vr,to:(t,n)=>new W(t,n)});var li=Rr.advance;import{createInterpolator as Oi,useIsomorphicLayoutEffect as Fi,useReducedMotion as ki,easings as Ui}from"@react-spring/shared";export*from"@react-spring/types";export{Ae as BailSignal,pe as Controller,J as FrameValue,Ar as Globals,W as Interpolation,js as Spring,Oe as SpringContext,ce as SpringRef,ae as SpringValue,Qs as Trail,Xs as Transition,ht as config,Oi as createInterpolator,Ui as easings,de as inferTo,ii as interpolate,si as to,li as update,Fr as useChain,As as useInView,Fi as useIsomorphicLayoutEffect,ki as useReducedMotion,gs as useResize,fs as useScroll,H as useSpring,Go as useSpringRef,Jo as useSpringValue,Ye as useSprings,Qt as useTrail,Gt as useTransition};
