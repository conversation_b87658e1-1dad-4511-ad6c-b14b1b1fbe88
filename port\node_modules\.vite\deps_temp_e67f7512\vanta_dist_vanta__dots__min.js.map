{"version": 3, "sources": ["../../vanta/dist/vanta.dots.min.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define([],e):\"object\"==typeof exports?exports._vantaEffect=e():t._vantaEffect=e()}(\"undefined\"!=typeof self?self:this,(()=>(()=>{\"use strict\";var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})}},e={};function i(t,e){return null==t&&(t=0),null==e&&(e=1),t+Math.random()*(e-t)}t.r(e),t.d(e,{default:()=>d}),Number.prototype.clamp=function(t,e){return Math.min(Math.max(this,t),e)};function s(t){for(;t.children&&t.children.length>0;)s(t.children[0]),t.remove(t.children[0]);t.geometry&&t.geometry.dispose(),t.material&&(Object.keys(t.material).forEach((e=>{t.material[e]&&null!==t.material[e]&&\"function\"==typeof t.material[e].dispose&&t.material[e].dispose()})),t.material.dispose())}const o=\"object\"==typeof window;let n=o&&window.THREE||{};o&&!window.VANTA&&(window.VANTA={});const r=o&&window.VANTA||{};r.register=(t,e)=>r[t]=t=>new e(t),r.version=\"0.5.24\";const h=function(){return Array.prototype.unshift.call(arguments,\"[VANTA]\"),console.error.apply(this,arguments)};r.VantaBase=class{constructor(t={}){if(!o)return!1;r.current=this,this.windowMouseMoveWrapper=this.windowMouseMoveWrapper.bind(this),this.windowTouchWrapper=this.windowTouchWrapper.bind(this),this.windowGyroWrapper=this.windowGyroWrapper.bind(this),this.resize=this.resize.bind(this),this.animationLoop=this.animationLoop.bind(this),this.restart=this.restart.bind(this);const e=\"function\"==typeof this.getDefaultOptions?this.getDefaultOptions():this.defaultOptions;if(this.options=Object.assign({mouseControls:!0,touchControls:!0,gyroControls:!1,minHeight:200,minWidth:200,scale:1,scaleMobile:1},e),(t instanceof HTMLElement||\"string\"==typeof t)&&(t={el:t}),Object.assign(this.options,t),this.options.THREE&&(n=this.options.THREE),this.el=this.options.el,null==this.el)h('Instance needs \"el\" param!');else if(!(this.options.el instanceof HTMLElement)){const t=this.el;if(this.el=(i=t,document.querySelector(i)),!this.el)return void h(\"Cannot find element\",t)}var i,s;this.prepareEl(),this.initThree(),this.setSize();try{this.init()}catch(t){return h(\"Init error\",t),this.renderer&&this.renderer.domElement&&this.el.removeChild(this.renderer.domElement),void(this.options.backgroundColor&&(console.log(\"[VANTA] Falling back to backgroundColor\"),this.el.style.background=(s=this.options.backgroundColor,\"number\"==typeof s?\"#\"+(\"00000\"+s.toString(16)).slice(-6):s)))}this.initMouse(),this.resize(),this.animationLoop();const a=window.addEventListener;a(\"resize\",this.resize),window.requestAnimationFrame(this.resize),this.options.mouseControls&&(a(\"scroll\",this.windowMouseMoveWrapper),a(\"mousemove\",this.windowMouseMoveWrapper)),this.options.touchControls&&(a(\"touchstart\",this.windowTouchWrapper),a(\"touchmove\",this.windowTouchWrapper)),this.options.gyroControls&&a(\"deviceorientation\",this.windowGyroWrapper)}setOptions(t={}){Object.assign(this.options,t),this.triggerMouseMove()}prepareEl(){let t,e;if(\"undefined\"!=typeof Node&&Node.TEXT_NODE)for(t=0;t<this.el.childNodes.length;t++){const e=this.el.childNodes[t];if(e.nodeType===Node.TEXT_NODE){const t=document.createElement(\"span\");t.textContent=e.textContent,e.parentElement.insertBefore(t,e),e.remove()}}for(t=0;t<this.el.children.length;t++)e=this.el.children[t],\"static\"===getComputedStyle(e).position&&(e.style.position=\"relative\"),\"auto\"===getComputedStyle(e).zIndex&&(e.style.zIndex=1);\"static\"===getComputedStyle(this.el).position&&(this.el.style.position=\"relative\")}applyCanvasStyles(t,e={}){Object.assign(t.style,{position:\"absolute\",zIndex:0,top:0,left:0,background:\"\"}),Object.assign(t.style,e),t.classList.add(\"vanta-canvas\")}initThree(){n.WebGLRenderer?(this.renderer=new n.WebGLRenderer({alpha:!0,antialias:!0}),this.el.appendChild(this.renderer.domElement),this.applyCanvasStyles(this.renderer.domElement),isNaN(this.options.backgroundAlpha)&&(this.options.backgroundAlpha=1),this.scene=new n.Scene):console.warn(\"[VANTA] No THREE defined on window\")}getCanvasElement(){return this.renderer?this.renderer.domElement:this.p5renderer?this.p5renderer.canvas:void 0}getCanvasRect(){const t=this.getCanvasElement();return!!t&&t.getBoundingClientRect()}windowMouseMoveWrapper(t){const e=this.getCanvasRect();if(!e)return!1;const i=t.clientX-e.left,s=t.clientY-e.top;i>=0&&s>=0&&i<=e.width&&s<=e.height&&(this.mouseX=i,this.mouseY=s,this.options.mouseEase||this.triggerMouseMove(i,s))}windowTouchWrapper(t){const e=this.getCanvasRect();if(!e)return!1;if(1===t.touches.length){const i=t.touches[0].clientX-e.left,s=t.touches[0].clientY-e.top;i>=0&&s>=0&&i<=e.width&&s<=e.height&&(this.mouseX=i,this.mouseY=s,this.options.mouseEase||this.triggerMouseMove(i,s))}}windowGyroWrapper(t){const e=this.getCanvasRect();if(!e)return!1;const i=Math.round(2*t.alpha)-e.left,s=Math.round(2*t.beta)-e.top;i>=0&&s>=0&&i<=e.width&&s<=e.height&&(this.mouseX=i,this.mouseY=s,this.options.mouseEase||this.triggerMouseMove(i,s))}triggerMouseMove(t,e){void 0===t&&void 0===e&&(this.options.mouseEase?(t=this.mouseEaseX,e=this.mouseEaseY):(t=this.mouseX,e=this.mouseY)),this.uniforms&&(this.uniforms.iMouse.value.x=t/this.scale,this.uniforms.iMouse.value.y=e/this.scale);const i=t/this.width,s=e/this.height;\"function\"==typeof this.onMouseMove&&this.onMouseMove(i,s)}setSize(){this.scale||(this.scale=1),\"undefined\"!=typeof navigator&&(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||window.innerWidth<600)&&this.options.scaleMobile?this.scale=this.options.scaleMobile:this.options.scale&&(this.scale=this.options.scale),this.width=Math.max(this.el.offsetWidth,this.options.minWidth),this.height=Math.max(this.el.offsetHeight,this.options.minHeight)}initMouse(){(!this.mouseX&&!this.mouseY||this.mouseX===this.options.minWidth/2&&this.mouseY===this.options.minHeight/2)&&(this.mouseX=this.width/2,this.mouseY=this.height/2,this.triggerMouseMove(this.mouseX,this.mouseY))}resize(){this.setSize(),this.camera&&(this.camera.aspect=this.width/this.height,\"function\"==typeof this.camera.updateProjectionMatrix&&this.camera.updateProjectionMatrix()),this.renderer&&(this.renderer.setSize(this.width,this.height),this.renderer.setPixelRatio(window.devicePixelRatio/this.scale)),\"function\"==typeof this.onResize&&this.onResize()}isOnScreen(){const t=this.el.offsetHeight,e=this.el.getBoundingClientRect(),i=window.pageYOffset||(document.documentElement||document.body.parentNode||document.body).scrollTop,s=e.top+i;return s-window.innerHeight<=i&&i<=s+t}animationLoop(){this.t||(this.t=0),this.t2||(this.t2=0);const t=performance.now();if(this.prevNow){let e=(t-this.prevNow)/(1e3/60);e=Math.max(.2,Math.min(e,5)),this.t+=e,this.t2+=(this.options.speed||1)*e,this.uniforms&&(this.uniforms.iTime.value=.016667*this.t2)}return this.prevNow=t,this.options.mouseEase&&(this.mouseEaseX=this.mouseEaseX||this.mouseX||0,this.mouseEaseY=this.mouseEaseY||this.mouseY||0,Math.abs(this.mouseEaseX-this.mouseX)+Math.abs(this.mouseEaseY-this.mouseY)>.1&&(this.mouseEaseX+=.05*(this.mouseX-this.mouseEaseX),this.mouseEaseY+=.05*(this.mouseY-this.mouseEaseY),this.triggerMouseMove(this.mouseEaseX,this.mouseEaseY))),(this.isOnScreen()||this.options.forceAnimate)&&(\"function\"==typeof this.onUpdate&&this.onUpdate(),this.scene&&this.camera&&(this.renderer.render(this.scene,this.camera),this.renderer.setClearColor(this.options.backgroundColor,this.options.backgroundAlpha)),this.fps&&this.fps.update&&this.fps.update(),\"function\"==typeof this.afterRender&&this.afterRender()),this.req=window.requestAnimationFrame(this.animationLoop)}restart(){if(this.scene)for(;this.scene.children.length;)this.scene.remove(this.scene.children[0]);\"function\"==typeof this.onRestart&&this.onRestart(),this.init()}init(){\"function\"==typeof this.onInit&&this.onInit()}destroy(){\"function\"==typeof this.onDestroy&&this.onDestroy();const t=window.removeEventListener;t(\"touchstart\",this.windowTouchWrapper),t(\"touchmove\",this.windowTouchWrapper),t(\"scroll\",this.windowMouseMoveWrapper),t(\"mousemove\",this.windowMouseMoveWrapper),t(\"deviceorientation\",this.windowGyroWrapper),t(\"resize\",this.resize),window.cancelAnimationFrame(this.req);const e=this.scene;e&&e.children&&s(e),this.renderer&&(this.renderer.domElement&&this.el.removeChild(this.renderer.domElement),this.renderer=null,this.scene=null),r.current===this&&(r.current=null)}};const a=r.VantaBase;let l=\"object\"==typeof window&&window.THREE;class c extends a{static initClass(){this.prototype.defaultOptions={color:16746528,color2:16746528,backgroundColor:2236962,size:3,spacing:35,showLines:!0}}onInit(){var t=this.camera=new l.PerspectiveCamera(50,this.width/this.height,.1,5e3);t.position.x=0,t.position.y=250,t.position.z=50,t.tx=0,t.ty=50,t.tz=350,t.lookAt(0,0,0),this.scene.add(t);var e,s,o,n,r,h,a,c=this.starsGeometry=new l.BufferGeometry,d=this.options.spacing;const p=[];for(e=o=-30;o<=30;e=++o)for(s=n=-30;n<=30;s=++n)(r=new l.Vector3).x=e*d+d/2,r.y=i(0,5)-150,r.z=s*d+d/2,p.push(r);if(c.setFromPoints(p),h=new l.PointsMaterial({color:this.options.color,size:this.options.size}),a=this.starField=new l.Points(c,h),this.scene.add(a),this.options.showLines){var u=new l.LineBasicMaterial({color:this.options.color2}),m=new l.BufferGeometry;const t=[];for(e=0;e<200;e++){var f=i(40,60),w=f+i(12,20),g=i(-1,1),y=Math.sqrt(1-g*g),v=i(0,2*Math.PI),M=Math.sin(v)*y,b=Math.cos(v)*y;t.push(new l.Vector3(b*f,M*f,g*f)),t.push(new l.Vector3(b*w,M*w,g*w))}m.setFromPoints(t),this.linesMesh=new l.LineSegments(m,u),this.scene.add(this.linesMesh)}}onUpdate(){const t=this.starsGeometry;this.starField;for(var e=0;e<t.attributes.position.array.length;e+=3){const i=t.attributes.position.array[e],s=t.attributes.position.array[e+1],o=t.attributes.position.array[e+2],n=s+.1*Math.sin(.02*o+.015*i+.02*this.t);t.attributes.position.array[e+1]=n}t.attributes.position.setUsage(l.DynamicDrawUsage),t.computeVertexNormals(),t.attributes.position.needsUpdate=!0;const i=this.camera,s=.003;i.position.x+=(i.tx-i.position.x)*s,i.position.y+=(i.ty-i.position.y)*s,i.position.z+=(i.tz-i.position.z)*s,i.lookAt(0,0,0),this.linesMesh&&(this.linesMesh.rotation.z+=.002,this.linesMesh.rotation.x+=8e-4,this.linesMesh.rotation.y+=5e-4)}onMouseMove(t,e){this.camera.tx=100*(t-.5),this.camera.ty=50+50*e}onRestart(){this.scene.remove(this.starField)}}c.initClass();const d=r.register(\"DOTS\",c);return e})()));"], "mappings": ";;;;;AAAA;AAAA;AAAA,MAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,eAAa,EAAE,IAAE,EAAE,eAAa,EAAE;AAAA,IAAC,GAAE,eAAa,OAAO,OAAK,OAAK,UAAM,OAAK,MAAI;AAAC;AAAa,UAAI,IAAE,EAAC,GAAE,CAACA,IAAEC,OAAI;AAAC,iBAAQC,MAAKD,GAAE,GAAE,EAAEA,IAAEC,EAAC,KAAG,CAAC,EAAE,EAAEF,IAAEE,EAAC,KAAG,OAAO,eAAeF,IAAEE,IAAE,EAAC,YAAW,MAAG,KAAID,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,GAAE,CAACC,IAAEH,OAAI,OAAO,UAAU,eAAe,KAAKG,IAAEH,EAAC,GAAE,GAAE,CAAAG,OAAG;AAAC,uBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,MAAC,EAAC,GAAE,IAAE,CAAC;AAAE,eAAS,EAAEA,IAAEH,IAAE;AAAC,eAAO,QAAMG,OAAIA,KAAE,IAAG,QAAMH,OAAIA,KAAE,IAAGG,KAAE,KAAK,OAAO,KAAGH,KAAEG;AAAA,MAAE;AAAC,QAAE,EAAE,CAAC,GAAE,EAAE,EAAE,GAAE,EAAC,SAAQ,MAAI,EAAC,CAAC,GAAE,OAAO,UAAU,QAAM,SAASA,IAAEH,IAAE;AAAC,eAAO,KAAK,IAAI,KAAK,IAAI,MAAKG,EAAC,GAAEH,EAAC;AAAA,MAAC;AAAE,eAAS,EAAEG,IAAE;AAAC,eAAKA,GAAE,YAAUA,GAAE,SAAS,SAAO,IAAG,GAAEA,GAAE,SAAS,CAAC,CAAC,GAAEA,GAAE,OAAOA,GAAE,SAAS,CAAC,CAAC;AAAE,QAAAA,GAAE,YAAUA,GAAE,SAAS,QAAQ,GAAEA,GAAE,aAAW,OAAO,KAAKA,GAAE,QAAQ,EAAE,SAAS,CAAAH,OAAG;AAAC,UAAAG,GAAE,SAASH,EAAC,KAAG,SAAOG,GAAE,SAASH,EAAC,KAAG,cAAY,OAAOG,GAAE,SAASH,EAAC,EAAE,WAASG,GAAE,SAASH,EAAC,EAAE,QAAQ;AAAA,QAAC,EAAE,GAAEG,GAAE,SAAS,QAAQ;AAAA,MAAE;AAAC,YAAM,IAAE,YAAU,OAAO;AAAO,UAAI,IAAE,KAAG,OAAO,SAAO,CAAC;AAAE,WAAG,CAAC,OAAO,UAAQ,OAAO,QAAM,CAAC;AAAG,YAAM,IAAE,KAAG,OAAO,SAAO,CAAC;AAAE,QAAE,WAAS,CAACA,IAAEH,OAAI,EAAEG,EAAC,IAAE,CAAAA,OAAG,IAAIH,GAAEG,EAAC,GAAE,EAAE,UAAQ;AAAS,YAAM,IAAE,WAAU;AAAC,eAAO,MAAM,UAAU,QAAQ,KAAK,WAAU,SAAS,GAAE,QAAQ,MAAM,MAAM,MAAK,SAAS;AAAA,MAAC;AAAE,QAAE,YAAU,MAAK;AAAA,QAAC,YAAYA,KAAE,CAAC,GAAE;AAAC,cAAG,CAAC,EAAE,QAAM;AAAG,YAAE,UAAQ,MAAK,KAAK,yBAAuB,KAAK,uBAAuB,KAAK,IAAI,GAAE,KAAK,qBAAmB,KAAK,mBAAmB,KAAK,IAAI,GAAE,KAAK,oBAAkB,KAAK,kBAAkB,KAAK,IAAI,GAAE,KAAK,SAAO,KAAK,OAAO,KAAK,IAAI,GAAE,KAAK,gBAAc,KAAK,cAAc,KAAK,IAAI,GAAE,KAAK,UAAQ,KAAK,QAAQ,KAAK,IAAI;AAAE,gBAAMH,KAAE,cAAY,OAAO,KAAK,oBAAkB,KAAK,kBAAkB,IAAE,KAAK;AAAe,cAAG,KAAK,UAAQ,OAAO,OAAO,EAAC,eAAc,MAAG,eAAc,MAAG,cAAa,OAAG,WAAU,KAAI,UAAS,KAAI,OAAM,GAAE,aAAY,EAAC,GAAEA,EAAC,IAAGG,cAAa,eAAa,YAAU,OAAOA,QAAKA,KAAE,EAAC,IAAGA,GAAC,IAAG,OAAO,OAAO,KAAK,SAAQA,EAAC,GAAE,KAAK,QAAQ,UAAQ,IAAE,KAAK,QAAQ,QAAO,KAAK,KAAG,KAAK,QAAQ,IAAG,QAAM,KAAK,GAAG,GAAE,4BAA4B;AAAA,mBAAU,EAAE,KAAK,QAAQ,cAAc,cAAa;AAAC,kBAAMA,KAAE,KAAK;AAAG,gBAAG,KAAK,MAAIF,KAAEE,IAAE,SAAS,cAAcF,EAAC,IAAG,CAAC,KAAK,GAAG,QAAO,KAAK,EAAE,uBAAsBE,EAAC;AAAA,UAAC;AAAC,cAAIF,IAAEC;AAAE,eAAK,UAAU,GAAE,KAAK,UAAU,GAAE,KAAK,QAAQ;AAAE,cAAG;AAAC,iBAAK,KAAK;AAAA,UAAC,SAAOC,IAAE;AAAC,mBAAO,EAAE,cAAaA,EAAC,GAAE,KAAK,YAAU,KAAK,SAAS,cAAY,KAAK,GAAG,YAAY,KAAK,SAAS,UAAU,GAAE,MAAK,KAAK,QAAQ,oBAAkB,QAAQ,IAAI,yCAAyC,GAAE,KAAK,GAAG,MAAM,cAAYD,KAAE,KAAK,QAAQ,iBAAgB,YAAU,OAAOA,KAAE,OAAK,UAAQA,GAAE,SAAS,EAAE,GAAG,MAAM,EAAE,IAAEA;AAAA,UAAI;AAAC,eAAK,UAAU,GAAE,KAAK,OAAO,GAAE,KAAK,cAAc;AAAE,gBAAME,KAAE,OAAO;AAAiB,UAAAA,GAAE,UAAS,KAAK,MAAM,GAAE,OAAO,sBAAsB,KAAK,MAAM,GAAE,KAAK,QAAQ,kBAAgBA,GAAE,UAAS,KAAK,sBAAsB,GAAEA,GAAE,aAAY,KAAK,sBAAsB,IAAG,KAAK,QAAQ,kBAAgBA,GAAE,cAAa,KAAK,kBAAkB,GAAEA,GAAE,aAAY,KAAK,kBAAkB,IAAG,KAAK,QAAQ,gBAAcA,GAAE,qBAAoB,KAAK,iBAAiB;AAAA,QAAC;AAAA,QAAC,WAAWD,KAAE,CAAC,GAAE;AAAC,iBAAO,OAAO,KAAK,SAAQA,EAAC,GAAE,KAAK,iBAAiB;AAAA,QAAC;AAAA,QAAC,YAAW;AAAC,cAAIA,IAAEH;AAAE,cAAG,eAAa,OAAO,QAAM,KAAK,UAAU,MAAIG,KAAE,GAAEA,KAAE,KAAK,GAAG,WAAW,QAAOA,MAAI;AAAC,kBAAMH,KAAE,KAAK,GAAG,WAAWG,EAAC;AAAE,gBAAGH,GAAE,aAAW,KAAK,WAAU;AAAC,oBAAMG,KAAE,SAAS,cAAc,MAAM;AAAE,cAAAA,GAAE,cAAYH,GAAE,aAAYA,GAAE,cAAc,aAAaG,IAAEH,EAAC,GAAEA,GAAE,OAAO;AAAA,YAAC;AAAA,UAAC;AAAC,eAAIG,KAAE,GAAEA,KAAE,KAAK,GAAG,SAAS,QAAOA,KAAI,CAAAH,KAAE,KAAK,GAAG,SAASG,EAAC,GAAE,aAAW,iBAAiBH,EAAC,EAAE,aAAWA,GAAE,MAAM,WAAS,aAAY,WAAS,iBAAiBA,EAAC,EAAE,WAASA,GAAE,MAAM,SAAO;AAAG,uBAAW,iBAAiB,KAAK,EAAE,EAAE,aAAW,KAAK,GAAG,MAAM,WAAS;AAAA,QAAW;AAAA,QAAC,kBAAkBG,IAAEH,KAAE,CAAC,GAAE;AAAC,iBAAO,OAAOG,GAAE,OAAM,EAAC,UAAS,YAAW,QAAO,GAAE,KAAI,GAAE,MAAK,GAAE,YAAW,GAAE,CAAC,GAAE,OAAO,OAAOA,GAAE,OAAMH,EAAC,GAAEG,GAAE,UAAU,IAAI,cAAc;AAAA,QAAC;AAAA,QAAC,YAAW;AAAC,YAAE,iBAAe,KAAK,WAAS,IAAI,EAAE,cAAc,EAAC,OAAM,MAAG,WAAU,KAAE,CAAC,GAAE,KAAK,GAAG,YAAY,KAAK,SAAS,UAAU,GAAE,KAAK,kBAAkB,KAAK,SAAS,UAAU,GAAE,MAAM,KAAK,QAAQ,eAAe,MAAI,KAAK,QAAQ,kBAAgB,IAAG,KAAK,QAAM,IAAI,EAAE,WAAO,QAAQ,KAAK,oCAAoC;AAAA,QAAC;AAAA,QAAC,mBAAkB;AAAC,iBAAO,KAAK,WAAS,KAAK,SAAS,aAAW,KAAK,aAAW,KAAK,WAAW,SAAO;AAAA,QAAM;AAAA,QAAC,gBAAe;AAAC,gBAAMA,KAAE,KAAK,iBAAiB;AAAE,iBAAM,CAAC,CAACA,MAAGA,GAAE,sBAAsB;AAAA,QAAC;AAAA,QAAC,uBAAuBA,IAAE;AAAC,gBAAMH,KAAE,KAAK,cAAc;AAAE,cAAG,CAACA,GAAE,QAAM;AAAG,gBAAMC,KAAEE,GAAE,UAAQH,GAAE,MAAKE,KAAEC,GAAE,UAAQH,GAAE;AAAI,UAAAC,MAAG,KAAGC,MAAG,KAAGD,MAAGD,GAAE,SAAOE,MAAGF,GAAE,WAAS,KAAK,SAAOC,IAAE,KAAK,SAAOC,IAAE,KAAK,QAAQ,aAAW,KAAK,iBAAiBD,IAAEC,EAAC;AAAA,QAAE;AAAA,QAAC,mBAAmBC,IAAE;AAAC,gBAAMH,KAAE,KAAK,cAAc;AAAE,cAAG,CAACA,GAAE,QAAM;AAAG,cAAG,MAAIG,GAAE,QAAQ,QAAO;AAAC,kBAAMF,KAAEE,GAAE,QAAQ,CAAC,EAAE,UAAQH,GAAE,MAAKE,KAAEC,GAAE,QAAQ,CAAC,EAAE,UAAQH,GAAE;AAAI,YAAAC,MAAG,KAAGC,MAAG,KAAGD,MAAGD,GAAE,SAAOE,MAAGF,GAAE,WAAS,KAAK,SAAOC,IAAE,KAAK,SAAOC,IAAE,KAAK,QAAQ,aAAW,KAAK,iBAAiBD,IAAEC,EAAC;AAAA,UAAE;AAAA,QAAC;AAAA,QAAC,kBAAkBC,IAAE;AAAC,gBAAMH,KAAE,KAAK,cAAc;AAAE,cAAG,CAACA,GAAE,QAAM;AAAG,gBAAMC,KAAE,KAAK,MAAM,IAAEE,GAAE,KAAK,IAAEH,GAAE,MAAKE,KAAE,KAAK,MAAM,IAAEC,GAAE,IAAI,IAAEH,GAAE;AAAI,UAAAC,MAAG,KAAGC,MAAG,KAAGD,MAAGD,GAAE,SAAOE,MAAGF,GAAE,WAAS,KAAK,SAAOC,IAAE,KAAK,SAAOC,IAAE,KAAK,QAAQ,aAAW,KAAK,iBAAiBD,IAAEC,EAAC;AAAA,QAAE;AAAA,QAAC,iBAAiBC,IAAEH,IAAE;AAAC,qBAASG,MAAG,WAASH,OAAI,KAAK,QAAQ,aAAWG,KAAE,KAAK,YAAWH,KAAE,KAAK,eAAaG,KAAE,KAAK,QAAOH,KAAE,KAAK,UAAS,KAAK,aAAW,KAAK,SAAS,OAAO,MAAM,IAAEG,KAAE,KAAK,OAAM,KAAK,SAAS,OAAO,MAAM,IAAEH,KAAE,KAAK;AAAO,gBAAMC,KAAEE,KAAE,KAAK,OAAMD,KAAEF,KAAE,KAAK;AAAO,wBAAY,OAAO,KAAK,eAAa,KAAK,YAAYC,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,UAAS;AAAC,eAAK,UAAQ,KAAK,QAAM,IAAG,eAAa,OAAO,cAAY,iEAAiE,KAAK,UAAU,SAAS,KAAG,OAAO,aAAW,QAAM,KAAK,QAAQ,cAAY,KAAK,QAAM,KAAK,QAAQ,cAAY,KAAK,QAAQ,UAAQ,KAAK,QAAM,KAAK,QAAQ,QAAO,KAAK,QAAM,KAAK,IAAI,KAAK,GAAG,aAAY,KAAK,QAAQ,QAAQ,GAAE,KAAK,SAAO,KAAK,IAAI,KAAK,GAAG,cAAa,KAAK,QAAQ,SAAS;AAAA,QAAC;AAAA,QAAC,YAAW;AAAC,WAAC,CAAC,KAAK,UAAQ,CAAC,KAAK,UAAQ,KAAK,WAAS,KAAK,QAAQ,WAAS,KAAG,KAAK,WAAS,KAAK,QAAQ,YAAU,OAAK,KAAK,SAAO,KAAK,QAAM,GAAE,KAAK,SAAO,KAAK,SAAO,GAAE,KAAK,iBAAiB,KAAK,QAAO,KAAK,MAAM;AAAA,QAAE;AAAA,QAAC,SAAQ;AAAC,eAAK,QAAQ,GAAE,KAAK,WAAS,KAAK,OAAO,SAAO,KAAK,QAAM,KAAK,QAAO,cAAY,OAAO,KAAK,OAAO,0BAAwB,KAAK,OAAO,uBAAuB,IAAG,KAAK,aAAW,KAAK,SAAS,QAAQ,KAAK,OAAM,KAAK,MAAM,GAAE,KAAK,SAAS,cAAc,OAAO,mBAAiB,KAAK,KAAK,IAAG,cAAY,OAAO,KAAK,YAAU,KAAK,SAAS;AAAA,QAAC;AAAA,QAAC,aAAY;AAAC,gBAAMC,KAAE,KAAK,GAAG,cAAaH,KAAE,KAAK,GAAG,sBAAsB,GAAEC,KAAE,OAAO,gBAAc,SAAS,mBAAiB,SAAS,KAAK,cAAY,SAAS,MAAM,WAAUC,KAAEF,GAAE,MAAIC;AAAE,iBAAOC,KAAE,OAAO,eAAaD,MAAGA,MAAGC,KAAEC;AAAA,QAAC;AAAA,QAAC,gBAAe;AAAC,eAAK,MAAI,KAAK,IAAE,IAAG,KAAK,OAAK,KAAK,KAAG;AAAG,gBAAMA,KAAE,YAAY,IAAI;AAAE,cAAG,KAAK,SAAQ;AAAC,gBAAIH,MAAGG,KAAE,KAAK,YAAU,MAAI;AAAI,YAAAH,KAAE,KAAK,IAAI,KAAG,KAAK,IAAIA,IAAE,CAAC,CAAC,GAAE,KAAK,KAAGA,IAAE,KAAK,OAAK,KAAK,QAAQ,SAAO,KAAGA,IAAE,KAAK,aAAW,KAAK,SAAS,MAAM,QAAM,WAAQ,KAAK;AAAA,UAAG;AAAC,iBAAO,KAAK,UAAQG,IAAE,KAAK,QAAQ,cAAY,KAAK,aAAW,KAAK,cAAY,KAAK,UAAQ,GAAE,KAAK,aAAW,KAAK,cAAY,KAAK,UAAQ,GAAE,KAAK,IAAI,KAAK,aAAW,KAAK,MAAM,IAAE,KAAK,IAAI,KAAK,aAAW,KAAK,MAAM,IAAE,QAAK,KAAK,cAAY,QAAK,KAAK,SAAO,KAAK,aAAY,KAAK,cAAY,QAAK,KAAK,SAAO,KAAK,aAAY,KAAK,iBAAiB,KAAK,YAAW,KAAK,UAAU,MAAK,KAAK,WAAW,KAAG,KAAK,QAAQ,kBAAgB,cAAY,OAAO,KAAK,YAAU,KAAK,SAAS,GAAE,KAAK,SAAO,KAAK,WAAS,KAAK,SAAS,OAAO,KAAK,OAAM,KAAK,MAAM,GAAE,KAAK,SAAS,cAAc,KAAK,QAAQ,iBAAgB,KAAK,QAAQ,eAAe,IAAG,KAAK,OAAK,KAAK,IAAI,UAAQ,KAAK,IAAI,OAAO,GAAE,cAAY,OAAO,KAAK,eAAa,KAAK,YAAY,IAAG,KAAK,MAAI,OAAO,sBAAsB,KAAK,aAAa;AAAA,QAAC;AAAA,QAAC,UAAS;AAAC,cAAG,KAAK,MAAM,QAAK,KAAK,MAAM,SAAS,SAAQ,MAAK,MAAM,OAAO,KAAK,MAAM,SAAS,CAAC,CAAC;AAAE,wBAAY,OAAO,KAAK,aAAW,KAAK,UAAU,GAAE,KAAK,KAAK;AAAA,QAAC;AAAA,QAAC,OAAM;AAAC,wBAAY,OAAO,KAAK,UAAQ,KAAK,OAAO;AAAA,QAAC;AAAA,QAAC,UAAS;AAAC,wBAAY,OAAO,KAAK,aAAW,KAAK,UAAU;AAAE,gBAAMA,KAAE,OAAO;AAAoB,UAAAA,GAAE,cAAa,KAAK,kBAAkB,GAAEA,GAAE,aAAY,KAAK,kBAAkB,GAAEA,GAAE,UAAS,KAAK,sBAAsB,GAAEA,GAAE,aAAY,KAAK,sBAAsB,GAAEA,GAAE,qBAAoB,KAAK,iBAAiB,GAAEA,GAAE,UAAS,KAAK,MAAM,GAAE,OAAO,qBAAqB,KAAK,GAAG;AAAE,gBAAMH,KAAE,KAAK;AAAM,UAAAA,MAAGA,GAAE,YAAU,EAAEA,EAAC,GAAE,KAAK,aAAW,KAAK,SAAS,cAAY,KAAK,GAAG,YAAY,KAAK,SAAS,UAAU,GAAE,KAAK,WAAS,MAAK,KAAK,QAAM,OAAM,EAAE,YAAU,SAAO,EAAE,UAAQ;AAAA,QAAK;AAAA,MAAC;AAAE,YAAM,IAAE,EAAE;AAAU,UAAI,IAAE,YAAU,OAAO,UAAQ,OAAO;AAAA,MAAM,MAAM,UAAU,EAAC;AAAA,QAAC,OAAO,YAAW;AAAC,eAAK,UAAU,iBAAe,EAAC,OAAM,UAAS,QAAO,UAAS,iBAAgB,SAAQ,MAAK,GAAE,SAAQ,IAAG,WAAU,KAAE;AAAA,QAAC;AAAA,QAAC,SAAQ;AAAC,cAAIG,KAAE,KAAK,SAAO,IAAI,EAAE,kBAAkB,IAAG,KAAK,QAAM,KAAK,QAAO,KAAG,GAAG;AAAE,UAAAA,GAAE,SAAS,IAAE,GAAEA,GAAE,SAAS,IAAE,KAAIA,GAAE,SAAS,IAAE,IAAGA,GAAE,KAAG,GAAEA,GAAE,KAAG,IAAGA,GAAE,KAAG,KAAIA,GAAE,OAAO,GAAE,GAAE,CAAC,GAAE,KAAK,MAAM,IAAIA,EAAC;AAAE,cAAIH,IAAEE,IAAEG,IAAEC,IAAEC,IAAEC,IAAEJ,IAAEK,KAAE,KAAK,gBAAc,IAAI,EAAE,kBAAeC,KAAE,KAAK,QAAQ;AAAQ,gBAAM,IAAE,CAAC;AAAE,eAAIV,KAAEK,KAAE,KAAIA,MAAG,IAAGL,KAAE,EAAEK,GAAE,MAAIH,KAAEI,KAAE,KAAIA,MAAG,IAAGJ,KAAE,EAAEI,GAAE,EAACC,KAAE,IAAI,EAAE,WAAS,IAAEP,KAAEU,KAAEA,KAAE,GAAEH,GAAE,IAAE,EAAE,GAAE,CAAC,IAAE,KAAIA,GAAE,IAAEL,KAAEQ,KAAEA,KAAE,GAAE,EAAE,KAAKH,EAAC;AAAE,cAAGE,GAAE,cAAc,CAAC,GAAED,KAAE,IAAI,EAAE,eAAe,EAAC,OAAM,KAAK,QAAQ,OAAM,MAAK,KAAK,QAAQ,KAAI,CAAC,GAAEJ,KAAE,KAAK,YAAU,IAAI,EAAE,OAAOK,IAAED,EAAC,GAAE,KAAK,MAAM,IAAIJ,EAAC,GAAE,KAAK,QAAQ,WAAU;AAAC,gBAAI,IAAE,IAAI,EAAE,kBAAkB,EAAC,OAAM,KAAK,QAAQ,OAAM,CAAC,GAAE,IAAE,IAAI,EAAE;AAAe,kBAAMD,KAAE,CAAC;AAAE,iBAAIH,KAAE,GAAEA,KAAE,KAAIA,MAAI;AAAC,kBAAI,IAAE,EAAE,IAAG,EAAE,GAAE,IAAE,IAAE,EAAE,IAAG,EAAE,GAAE,IAAE,EAAE,IAAG,CAAC,GAAE,IAAE,KAAK,KAAK,IAAE,IAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,KAAK,EAAE,GAAE,IAAE,KAAK,IAAI,CAAC,IAAE,GAAE,IAAE,KAAK,IAAI,CAAC,IAAE;AAAE,cAAAG,GAAE,KAAK,IAAI,EAAE,QAAQ,IAAE,GAAE,IAAE,GAAE,IAAE,CAAC,CAAC,GAAEA,GAAE,KAAK,IAAI,EAAE,QAAQ,IAAE,GAAE,IAAE,GAAE,IAAE,CAAC,CAAC;AAAA,YAAC;AAAC,cAAE,cAAcA,EAAC,GAAE,KAAK,YAAU,IAAI,EAAE,aAAa,GAAE,CAAC,GAAE,KAAK,MAAM,IAAI,KAAK,SAAS;AAAA,UAAC;AAAA,QAAC;AAAA,QAAC,WAAU;AAAC,gBAAMA,KAAE,KAAK;AAAc,eAAK;AAAU,mBAAQH,KAAE,GAAEA,KAAEG,GAAE,WAAW,SAAS,MAAM,QAAOH,MAAG,GAAE;AAAC,kBAAMC,KAAEE,GAAE,WAAW,SAAS,MAAMH,EAAC,GAAEE,KAAEC,GAAE,WAAW,SAAS,MAAMH,KAAE,CAAC,GAAEK,KAAEF,GAAE,WAAW,SAAS,MAAMH,KAAE,CAAC,GAAEM,KAAEJ,KAAE,MAAG,KAAK,IAAI,OAAIG,KAAE,QAAKJ,KAAE,OAAI,KAAK,CAAC;AAAE,YAAAE,GAAE,WAAW,SAAS,MAAMH,KAAE,CAAC,IAAEM;AAAA,UAAC;AAAC,UAAAH,GAAE,WAAW,SAAS,SAAS,EAAE,gBAAgB,GAAEA,GAAE,qBAAqB,GAAEA,GAAE,WAAW,SAAS,cAAY;AAAG,gBAAMF,KAAE,KAAK,QAAOC,KAAE;AAAK,UAAAD,GAAE,SAAS,MAAIA,GAAE,KAAGA,GAAE,SAAS,KAAGC,IAAED,GAAE,SAAS,MAAIA,GAAE,KAAGA,GAAE,SAAS,KAAGC,IAAED,GAAE,SAAS,MAAIA,GAAE,KAAGA,GAAE,SAAS,KAAGC,IAAED,GAAE,OAAO,GAAE,GAAE,CAAC,GAAE,KAAK,cAAY,KAAK,UAAU,SAAS,KAAG,MAAK,KAAK,UAAU,SAAS,KAAG,MAAK,KAAK,UAAU,SAAS,KAAG;AAAA,QAAK;AAAA,QAAC,YAAYE,IAAEH,IAAE;AAAC,eAAK,OAAO,KAAG,OAAKG,KAAE,MAAI,KAAK,OAAO,KAAG,KAAG,KAAGH;AAAA,QAAC;AAAA,QAAC,YAAW;AAAC,eAAK,MAAM,OAAO,KAAK,SAAS;AAAA,QAAC;AAAA,MAAC;AAAC,QAAE,UAAU;AAAE,YAAM,IAAE,EAAE,SAAS,QAAO,CAAC;AAAE,aAAO;AAAA,IAAC,GAAG,EAAE;AAAA;AAAA;", "names": ["e", "i", "s", "t", "a", "o", "n", "r", "h", "c", "d"]}