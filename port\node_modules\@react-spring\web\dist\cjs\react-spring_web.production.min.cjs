"use strict";var k=Object.defineProperty;var T=Object.getOwnPropertyDescriptor;var j=Object.getOwnPropertyNames;var N=Object.prototype.hasOwnProperty;var R=(t,e)=>{for(var o in e)k(t,o,{get:e[o],enumerable:!0})},v=(t,e,o,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of j(e))!N.call(t,s)&&s!==o&&k(t,s,{get:()=>e[s],enumerable:!(i=T(e,s))||i.enumerable});return t},l=(t,e,o)=>(v(t,e,"default"),o&&v(o,e,"default"));var W=t=>v(k({},"__esModule",{value:!0}),t);var u={};R(u,{a:()=>q,animated:()=>q});module.exports=W(u);var L=require("@react-spring/core"),E=require("react-dom"),O=require("@react-spring/shared"),P=require("@react-spring/animated");var C=/^--/;function _(t,e){return e==null||typeof e=="boolean"||e===""?"":typeof e=="number"&&e!==0&&!C.test(t)&&!(g.hasOwnProperty(t)&&g[t])?e+"px":(""+e).trim()}var A={};function I(t,e){if(!t.nodeType||!t.setAttribute)return!1;let o=t.nodeName==="filter"||t.parentNode&&t.parentNode.nodeName==="filter",{className:i,style:s,children:d,scrollTop:m,scrollLeft:p,viewBox:a,...f}=e,b=Object.values(f),h=Object.keys(f).map(n=>o||t.hasAttribute(n)?n:A[n]||(A[n]=n.replace(/([A-Z])/g,c=>"-"+c.toLowerCase())));d!==void 0&&(t.textContent=d);for(let n in s)if(s.hasOwnProperty(n)){let c=_(n,s[n]);C.test(n)?t.style.setProperty(n,c):t.style[n]=c}h.forEach((n,c)=>{t.setAttribute(n,b[c])}),i!==void 0&&(t.className=i),m!==void 0&&(t.scrollTop=m),p!==void 0&&(t.scrollLeft=p),a!==void 0&&t.setAttribute("viewBox",a)}var g={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},$=(t,e)=>t+e.charAt(0).toUpperCase()+e.substring(1),G=["Webkit","Ms","Moz","O"];g=Object.keys(g).reduce((t,e)=>(G.forEach(o=>t[$(o,e)]=t[e]),t),g);var F=require("@react-spring/animated"),r=require("@react-spring/shared"),M=/^(matrix|translate|scale|rotate|skew)/,U=/^(translate)/,D=/^(rotate|skew)/,V=(t,e)=>r.is.num(t)&&t!==0?t+e:t,y=(t,e)=>r.is.arr(t)?t.every(o=>y(o,e)):r.is.num(t)?t===e:parseFloat(t)===e,x=class extends F.AnimatedObject{constructor({x:e,y:o,z:i,...s}){let d=[],m=[];(e||o||i)&&(d.push([e||0,o||0,i||0]),m.push(p=>[`translate3d(${p.map(a=>V(a,"px")).join(",")})`,y(p,0)])),(0,r.eachProp)(s,(p,a)=>{if(a==="transform")d.push([p||""]),m.push(f=>[f,f===""]);else if(M.test(a)){if(delete s[a],r.is.und(p))return;let f=U.test(a)?"px":D.test(a)?"deg":"";d.push((0,r.toArray)(p)),m.push(a==="rotate3d"?([b,h,n,c])=>[`rotate3d(${b},${h},${n},${V(c,f)})`,y(c,0)]:b=>[`${a}(${b.map(h=>V(h,f)).join(",")})`,y(b,a.startsWith("scale")?1:0)])}}),d.length&&(s.transform=new w(d,m)),super(s)}},w=class extends r.FluidValue{constructor(o,i){super();this.inputs=o;this.transforms=i;this._value=null}get(){return this._value||(this._value=this._get())}_get(){let o="",i=!0;return(0,r.each)(this.inputs,(s,d)=>{let m=(0,r.getFluidValue)(s[0]),[p,a]=this.transforms[d](r.is.arr(m)?m:s.map(r.getFluidValue));o+=" "+p,i=i&&a}),i?"none":o}observerAdded(o){o==1&&(0,r.each)(this.inputs,i=>(0,r.each)(i,s=>(0,r.hasFluidValue)(s)&&(0,r.addFluidObserver)(s,this)))}observerRemoved(o){o==0&&(0,r.each)(this.inputs,i=>(0,r.each)(i,s=>(0,r.hasFluidValue)(s)&&(0,r.removeFluidObserver)(s,this)))}eventObserved(o){o.type=="change"&&(this._value=null),(0,r.callFluidObservers)(this,o)}};var S=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"];l(u,require("@react-spring/core"),module.exports);L.Globals.assign({batchedUpdates:E.unstable_batchedUpdates,createStringInterpolator:O.createStringInterpolator,colors:O.colors});var H=(0,P.createHost)(S,{applyAnimatedValues:I,createAnimatedStyle:t=>new x(t),getComponentProps:({scrollTop:t,scrollLeft:e,...o})=>o}),q=H.animated;0&&(module.exports={a,animated,...require("@react-spring/core")});
