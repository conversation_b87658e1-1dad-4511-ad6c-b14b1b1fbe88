var ze=Object.defineProperty;var Le=(e,t)=>{for(var r in t)ze(e,r,{get:t[r],enumerable:!0})};var p={};Le(p,{assign:()=>U,colors:()=>c,createStringInterpolator:()=>k,skipAnimation:()=>ee,to:()=>J,willAdvance:()=>S});import{raf as I}from"@react-spring/rafz";function Y(){}var mt=(e,t,r)=>Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0}),l={arr:Array.isArray,obj:e=>!!e&&e.constructor.name==="Object",fun:e=>typeof e=="function",str:e=>typeof e=="string",num:e=>typeof e=="number",und:e=>e===void 0};function bt(e,t){if(l.arr(e)){if(!l.arr(t)||e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}return e===t}var Ve=(e,t)=>e.forEach(t);function xt(e,t,r){if(l.arr(e)){for(let n=0;n<e.length;n++)t.call(r,e[n],`${n}`);return}for(let n in e)e.hasOwnProperty(n)&&t.call(r,e[n],n)}var ht=e=>l.und(e)?[]:l.arr(e)?e:[e];function Pe(e,t){if(e.size){let r=Array.from(e);e.clear(),Ve(r,t)}}var yt=(e,...t)=>Pe(e,r=>r(...t)),h=()=>typeof window>"u"||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent);var k,J,c=null,ee=!1,S=Y,U=e=>{e.to&&(J=e.to),e.now&&(I.now=e.now),e.colors!==void 0&&(c=e.colors),e.skipAnimation!=null&&(ee=e.skipAnimation),e.createStringInterpolator&&(k=e.createStringInterpolator),e.requestAnimationFrame&&I.use(e.requestAnimationFrame),e.batchedUpdates&&(I.batchedUpdates=e.batchedUpdates),e.willAdvance&&(S=e.willAdvance),e.frameLoop&&(I.frameLoop=e.frameLoop)};import{raf as R}from"@react-spring/rafz";var E=new Set,u=[],H=[],A=0,qe={get idle(){return!E.size&&!u.length},start(e){A>e.priority?(E.add(e),R.onStart($e)):(te(e),R(B))},advance:B,sort(e){if(A)R.onFrame(()=>qe.sort(e));else{let t=u.indexOf(e);~t&&(u.splice(t,1),re(e))}},clear(){u=[],E.clear()}};function $e(){E.forEach(te),E.clear(),R(B)}function te(e){u.includes(e)||re(e)}function re(e){u.splice(Ge(u,t=>t.priority>e.priority),0,e)}function B(e){let t=H;for(let r=0;r<u.length;r++){let n=u[r];A=n.priority,n.idle||(S(n),n.advance(e),n.idle||t.push(n))}return A=0,H=u,H.length=0,u=t,u.length>0}function Ge(e,t){let r=e.findIndex(t);return r<0?e.length:r}var ne=(e,t,r)=>Math.min(Math.max(r,e),t);var It={transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199};var d="[-+]?\\d*\\.?\\d+",M=d+"%";function C(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var oe=new RegExp("rgb"+C(d,d,d)),fe=new RegExp("rgba"+C(d,d,d,d)),ae=new RegExp("hsl"+C(d,M,M)),ie=new RegExp("hsla"+C(d,M,M,d)),se=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,ue=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,le=/^#([0-9a-fA-F]{6})$/,ce=/^#([0-9a-fA-F]{8})$/;function be(e){let t;return typeof e=="number"?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=le.exec(e))?parseInt(t[1]+"ff",16)>>>0:c&&c[e]!==void 0?c[e]:(t=oe.exec(e))?(y(t[1])<<24|y(t[2])<<16|y(t[3])<<8|255)>>>0:(t=fe.exec(e))?(y(t[1])<<24|y(t[2])<<16|y(t[3])<<8|me(t[4]))>>>0:(t=se.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=ce.exec(e))?parseInt(t[1],16)>>>0:(t=ue.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=ae.exec(e))?(de(pe(t[1]),z(t[2]),z(t[3]))|255)>>>0:(t=ie.exec(e))?(de(pe(t[1]),z(t[2]),z(t[3]))|me(t[4]))>>>0:null}function j(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function de(e,t,r){let n=r<.5?r*(1+t):r+t-r*t,f=2*r-n,o=j(f,n,e+1/3),i=j(f,n,e),s=j(f,n,e-1/3);return Math.round(o*255)<<24|Math.round(i*255)<<16|Math.round(s*255)<<8}function y(e){let t=parseInt(e,10);return t<0?0:t>255?255:t}function pe(e){return(parseFloat(e)%360+360)%360/360}function me(e){let t=parseFloat(e);return t<0?0:t>1?255:Math.round(t*255)}function z(e){let t=parseFloat(e);return t<0?0:t>100?1:t/100}function D(e){let t=be(e);if(t===null)return e;t=t||0;let r=(t&4278190080)>>>24,n=(t&16711680)>>>16,f=(t&65280)>>>8,o=(t&255)/255;return`rgba(${r}, ${n}, ${f}, ${o})`}var W=(e,t,r)=>{if(l.fun(e))return e;if(l.arr(e))return W({range:e,output:t,extrapolate:r});if(l.str(e.output[0]))return k(e);let n=e,f=n.output,o=n.range||[0,1],i=n.extrapolateLeft||n.extrapolate||"extend",s=n.extrapolateRight||n.extrapolate||"extend",x=n.easing||(a=>a);return a=>{let F=He(a,o);return Ue(a,o[F],o[F+1],f[F],f[F+1],x,i,s,n.map)}};function Ue(e,t,r,n,f,o,i,s,x){let a=x?x(e):e;if(a<t){if(i==="identity")return a;i==="clamp"&&(a=t)}if(a>r){if(s==="identity")return a;s==="clamp"&&(a=r)}return n===f?n:t===r?e<=t?n:f:(t===-1/0?a=-a:r===1/0?a=a-t:a=(a-t)/(r-t),a=o(a),n===-1/0?a=-a:f===1/0?a=a+n:a=a*(f-n)+n,a)}function He(e,t){for(var r=1;r<t.length-1&&!(t[r]>=e);++r);return r-1}var Be=(e,t="end")=>r=>{r=t==="end"?Math.min(r,.999):Math.max(r,.001);let n=r*e,f=t==="end"?Math.floor(n):Math.ceil(n);return ne(0,1,f/e)},P=1.70158,L=P*1.525,xe=P+1,he=2*Math.PI/3,ye=2*Math.PI/4.5,V=e=>e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375,Lt={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>1-(1-e)*(1-e),easeInOutQuad:e=>e<.5?2*e*e:1-Math.pow(-2*e+2,2)/2,easeInCubic:e=>e*e*e,easeOutCubic:e=>1-Math.pow(1-e,3),easeInOutCubic:e=>e<.5?4*e*e*e:1-Math.pow(-2*e+2,3)/2,easeInQuart:e=>e*e*e*e,easeOutQuart:e=>1-Math.pow(1-e,4),easeInOutQuart:e=>e<.5?8*e*e*e*e:1-Math.pow(-2*e+2,4)/2,easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>1-Math.pow(1-e,5),easeInOutQuint:e=>e<.5?16*e*e*e*e*e:1-Math.pow(-2*e+2,5)/2,easeInSine:e=>1-Math.cos(e*Math.PI/2),easeOutSine:e=>Math.sin(e*Math.PI/2),easeInOutSine:e=>-(Math.cos(Math.PI*e)-1)/2,easeInExpo:e=>e===0?0:Math.pow(2,10*e-10),easeOutExpo:e=>e===1?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>e===0?0:e===1?1:e<.5?Math.pow(2,20*e-10)/2:(2-Math.pow(2,-20*e+10))/2,easeInCirc:e=>1-Math.sqrt(1-Math.pow(e,2)),easeOutCirc:e=>Math.sqrt(1-Math.pow(e-1,2)),easeInOutCirc:e=>e<.5?(1-Math.sqrt(1-Math.pow(2*e,2)))/2:(Math.sqrt(1-Math.pow(-2*e+2,2))+1)/2,easeInBack:e=>xe*e*e*e-P*e*e,easeOutBack:e=>1+xe*Math.pow(e-1,3)+P*Math.pow(e-1,2),easeInOutBack:e=>e<.5?Math.pow(2*e,2)*((L+1)*2*e-L)/2:(Math.pow(2*e-2,2)*((L+1)*(e*2-2)+L)+2)/2,easeInElastic:e=>e===0?0:e===1?1:-Math.pow(2,10*e-10)*Math.sin((e*10-10.75)*he),easeOutElastic:e=>e===0?0:e===1?1:Math.pow(2,-10*e)*Math.sin((e*10-.75)*he)+1,easeInOutElastic:e=>e===0?0:e===1?1:e<.5?-(Math.pow(2,20*e-10)*Math.sin((20*e-11.125)*ye))/2:Math.pow(2,-20*e+10)*Math.sin((20*e-11.125)*ye)/2+1,easeInBounce:e=>1-V(1-e),easeOutBounce:V,easeInOutBounce:e=>e<.5?(1-V(1-2*e))/2:(1+V(2*e-1))/2,steps:Be};var g=Symbol.for("FluidValue.get"),m=Symbol.for("FluidValue.observers");var Pt=e=>!!(e&&e[g]),ve=e=>e&&e[g]?e[g]():e,qt=e=>e[m]||null;function je(e,t){e.eventObserved?e.eventObserved(t):e(t)}function $t(e,t){let r=e[m];r&&r.forEach(n=>{je(n,t)})}g,m;var ge=class{constructor(t){if(!t&&!(t=this.get))throw Error("Unknown getter");De(this,t)}},De=(e,t)=>Ee(e,g,t);function Gt(e,t){if(e[g]){let r=e[m];r||Ee(e,m,r=new Set),r.has(t)||(r.add(t),e.observerAdded&&e.observerAdded(r.size,t))}return t}function Qt(e,t){let r=e[m];if(r&&r.has(t)){let n=r.size-1;n?r.delete(t):e[m]=null,e.observerRemoved&&e.observerRemoved(n,t)}}var Ee=(e,t,r)=>Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0});var O=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,Oe=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,K=new RegExp(`(${O.source})(%|[a-z]+)`,"i"),we=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,b=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;var N=e=>{let[t,r]=We(e);if(!t||h())return e;let n=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(n)return n.trim();if(r&&r.startsWith("--")){let f=window.getComputedStyle(document.documentElement).getPropertyValue(r);return f||e}else{if(r&&b.test(r))return N(r);if(r)return r}return e},We=e=>{let t=b.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]};var _,Ke=(e,t,r,n,f)=>`rgba(${Math.round(t)}, ${Math.round(r)}, ${Math.round(n)}, ${f})`,Xt=e=>{_||(_=c?new RegExp(`(${Object.keys(c).join("|")})(?!\\w)`,"g"):/^\b$/);let t=e.output.map(o=>ve(o).replace(b,N).replace(Oe,D).replace(_,D)),r=t.map(o=>o.match(O).map(Number)),f=r[0].map((o,i)=>r.map(s=>{if(!(i in s))throw Error('The arity of each "output" value must be equal');return s[i]})).map(o=>W({...e,output:o}));return o=>{let i=!K.test(t[0])&&t.find(x=>K.test(x))?.replace(O,""),s=0;return t[0].replace(O,()=>`${f[s++](o)}${i||""}`).replace(we,Ke)}};var Z="react-spring: ",Te=e=>{let t=e,r=!1;if(typeof t!="function")throw new TypeError(`${Z}once requires a function parameter`);return(...n)=>{r||(t(...n),r=!0)}},Ne=Te(console.warn);function Jt(){Ne(`${Z}The "interpolate" function is deprecated in v9 (use "to" instead)`)}var _e=Te(console.warn);function er(){_e(`${Z}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`)}function or(e){return l.str(e)&&(e[0]=="#"||/\d/.test(e)||!h()&&b.test(e)||e in(c||{}))}import{raf as Re}from"@react-spring/rafz";var v,q=new WeakMap,Ze=e=>e.forEach(({target:t,contentRect:r})=>q.get(t)?.forEach(n=>n(r)));function Fe(e,t){v||typeof ResizeObserver<"u"&&(v=new ResizeObserver(Ze));let r=q.get(t);return r||(r=new Set,q.set(t,r)),r.add(e),v&&v.observe(t),()=>{let n=q.get(t);n&&(n.delete(e),!n.size&&v&&v.unobserve(t))}}var $=new Set,w,Xe=()=>{let e=()=>{$.forEach(t=>t({width:window.innerWidth,height:window.innerHeight}))};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},Ie=e=>($.add(e),w||(w=Xe()),()=>{$.delete(e),!$.size&&w&&(w(),w=void 0)});var ke=(e,{container:t=document.documentElement}={})=>t===document.documentElement?Ie(e):Fe(e,t);var Se=(e,t,r)=>t-e===0?1:(r-e)/(t-e);var Ye={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}},G=class{constructor(t,r){this.createAxis=()=>({current:0,progress:0,scrollLength:0});this.updateAxis=t=>{let r=this.info[t],{length:n,position:f}=Ye[t];r.current=this.container[`scroll${f}`],r.scrollLength=this.container[`scroll${n}`]-this.container[`client${n}`],r.progress=Se(0,r.scrollLength,r.current)};this.update=()=>{this.updateAxis("x"),this.updateAxis("y")};this.sendEvent=()=>{this.callback(this.info)};this.advance=()=>{this.update(),this.sendEvent()};this.callback=t,this.container=r,this.info={time:0,x:this.createAxis(),y:this.createAxis()}}};var T=new WeakMap,Ae=new WeakMap,X=new WeakMap,Me=e=>e===document.documentElement?window:e,yr=(e,{container:t=document.documentElement}={})=>{let r=X.get(t);r||(r=new Set,X.set(t,r));let n=new G(e,t);if(r.add(n),!T.has(t)){let o=()=>(r?.forEach(s=>s.advance()),!0);T.set(t,o);let i=Me(t);window.addEventListener("resize",o,{passive:!0}),t!==document.documentElement&&Ae.set(t,ke(o,{container:t})),i.addEventListener("scroll",o,{passive:!0})}let f=T.get(t);return Re(f),()=>{Re.cancel(f);let o=X.get(t);if(!o||(o.delete(n),o.size))return;let i=T.get(t);T.delete(t),i&&(Me(t).removeEventListener("scroll",i),window.removeEventListener("resize",i),Ae.get(t)?.())}};import{useRef as Je}from"react";function Er(e){let t=Je(null);return t.current===null&&(t.current=e()),t.current}import{useState as nt}from"react";import{useRef as rt}from"react";import{useEffect as et,useLayoutEffect as tt}from"react";var Q=h()?et:tt;var Ce=()=>{let e=rt(!1);return Q(()=>(e.current=!0,()=>{e.current=!1}),[]),e};function Mr(){let e=nt()[1],t=Ce();return()=>{t.current&&e(Math.random())}}import{useEffect as ot,useRef as ft,useState as at}from"react";function Lr(e,t){let[r]=at(()=>({inputs:t,result:e()})),n=ft(void 0),f=n.current,o=f;return o?t&&o.inputs&&it(t,o.inputs)||(o={inputs:t,result:e()}):o=r,ot(()=>{n.current=o,f==r&&(r.inputs=r.result=void 0)},[o]),o.result}function it(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}import{useEffect as st}from"react";var $r=e=>st(e,ut),ut=[];import{useEffect as lt,useRef as ct}from"react";function Ur(e){let t=ct(void 0);return lt(()=>{t.current=e}),t.current}import{useState as dt}from"react";var Wr=()=>{let[e,t]=dt(null);return Q(()=>{let r=window.matchMedia("(prefers-reduced-motion)"),n=f=>{t(f.matches),U({skipAnimation:f.matches})};return n(r),r.addEventListener?r.addEventListener("change",n):r.addListener(n),()=>{r.removeEventListener?r.removeEventListener("change",n):r.removeListener(n)}},[]),e};import{raf as yn}from"@react-spring/rafz";export{ge as FluidValue,p as Globals,Gt as addFluidObserver,je as callFluidObserver,$t as callFluidObservers,ne as clamp,D as colorToRgba,It as colors,W as createInterpolator,Xt as createStringInterpolator,mt as defineHidden,er as deprecateDirectCall,Jt as deprecateInterpolate,Ve as each,xt as eachProp,Lt as easings,Pe as flush,yt as flushCalls,qe as frameLoop,qt as getFluidObservers,ve as getFluidValue,Pt as hasFluidValue,se as hex3,ue as hex4,le as hex6,ce as hex8,ae as hsl,ie as hsla,l as is,or as isAnimatedString,bt as isEqual,h as isSSR,Y as noop,ke as onResize,yr as onScroll,Te as once,Z as prefix,yn as raf,Qt as removeFluidObserver,oe as rgb,fe as rgba,De as setFluidGetter,ht as toArray,Er as useConstant,Mr as useForceUpdate,Q as useIsomorphicLayoutEffect,Lr as useMemoOne,$r as useOnce,Ur as usePrev,Wr as useReducedMotion};
