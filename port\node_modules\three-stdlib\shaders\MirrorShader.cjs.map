{"version": 3, "file": "MirrorShader.cjs", "sources": ["../../src/shaders/MirrorShader.ts"], "sourcesContent": ["/**\n * Mirror Shader\n * Copies half the input to the other half\n *\n * side: side of input to mirror (0 = left, 1 = right, 2 = top, 3 = bottom)\n */\n\nexport const MirrorShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    side: { value: 1 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform int side;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 p = vUv;\n    \tif (side == 0){\n    \t\tif (p.x > 0.5) p.x = 1.0 - p.x;\n    \t}else if (side == 1){\n    \t\tif (p.x < 0.5) p.x = 1.0 - p.x;\n    \t}else if (side == 2){\n    \t\tif (p.y < 0.5) p.y = 1.0 - p.y;\n    \t}else if (side == 3){\n    \t\tif (p.y > 0.5) p.y = 1.0 - p.y;\n    \t} \n    \tvec4 color = texture2D(tDiffuse, p);\n    \tgl_FragColor = color;\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAOO,MAAM,eAAe;AAAA,EAC1B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,MAAM,EAAE,OAAO,EAAE;AAAA,EACnB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuB7B;;"}