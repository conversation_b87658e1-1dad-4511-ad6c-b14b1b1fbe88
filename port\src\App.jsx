import { useEffect, useState } from 'react'
import './App.css'
import  DOTS from 'vanta/dist/vanta.dots.min'
import { Link } from "react-router-dom";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";


import Navbar from "./components/Navbar";

import Home from "./pages/Home";


function App() {
  useEffect(()=>{
    DOTS({
      el:'#vanta',
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      color: 0xffffff,
      color2: 0xffffff,
      backgroundColor: 0x000000,
      showLines: false
    })

  },[]);
  
  return (
    <>
     <div className="app">
        <div className="bg" id="vanta"></div>
        <div className='navbar'>
          <Navbar/>
        </div>
        
          
        
        
     </div>
    </>
  )
}

export default App
