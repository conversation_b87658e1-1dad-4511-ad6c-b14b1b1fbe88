module.exports={A:{A:{"2":"K D E uC","520":"F A B"},B:{"1":"0 1 2 3 4 5 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB I VB","8":"C L","388":"M G N O P"},C:{"1":"0 1 2 3 4 5 EB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B SC 2B TC 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC Q H R UC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB I VB VC KC WC wC xC","2":"vC RC yC zC","132":"6 7 8 9 J WB K D E F A B C L M G N O P XB AB BB CB DB"},D:{"1":"0 1 2 3 4 5 BB CB DB EB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B SC 2B TC 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB I VB VC KC WC","2":"J WB","132":"6 7 8 9 K D E F A B C L M G N O P XB AB"},E:{"2":"0C","8":"J WB XC 1C","520":"K D E F A B C 2C 3C 4C YC LC","16385":"OC bC cC dC eC fC 9C PC gC hC iC jC kC AD QC lC mC nC oC pC qC rC BD","17412":"L MC 5C","23556":"M","24580":"G 6C 7C ZC aC NC 8C"},F:{"1":"0 1 2 3 4 5 6 7 8 9 N O P XB AB BB CB DB EB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC Q H R UC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z","2":"F CD DD ED","132":"B C G FD LC sC GD MC"},G:{"2":"E XC HD tC ID JD KD LD MD ND OD PD QD RD SD","16385":"jC kC dD QC lC mC nC oC pC qC rC","17412":"TD UD VD WD XD","19460":"YD ZD aD ZC aC NC bD OC bC cC dC eC fC cD PC gC hC iC"},H:{"2":"eD"},I:{"1":"I","2":"fD gD","132":"RC J hD iD tC jD kD"},J:{"2":"D A"},K:{"1":"H","2":"A B C LC sC MC"},L:{"1":"I"},M:{"1":"KC"},N:{"8":"A B"},O:{"1":"NC"},P:{"1":"6 7 8 9 AB BB CB DB EB lD mD nD oD pD YC qD rD sD tD uD OC PC QC vD","132":"J"},Q:{"1":"wD"},R:{"1":"xD"},S:{"1":"yD zD"}},B:6,C:"WebM video format",D:true};
