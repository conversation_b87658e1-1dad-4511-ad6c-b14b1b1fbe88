.container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    pointer-events: none;
  }
  
  .main > div {
    position: absolute;
    will-change: transform;
    border-radius: 50%;
    background: black;
    box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
    opacity: 0.6;
  }
  
  .main > div:nth-child(1) {
    width: 30px;
    height: 30px;
  }

  .main > div:nth-child(2) {
    width: 60px;
    height: 60px;
  }

  .main > div:nth-child(3) {
    width: 40px;
    height: 40px;
  }
  
  .main > div::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 0, 0, 0.8);
  }

  .main > div:nth-child(2)::after {
    top: 18px;
    left: 18px;
    width: 18px;
    height: 18px;
  }

  .main > div:nth-child(3)::after {
    top: 13px;
    left: 13px;
    width: 13px;
    height: 13px;
  }
  
  .main {
    position: absolute;
    width: 100%;
    height: 100%;
    filter: url('#blob');
    overflow: hidden;
    background: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: default;
    pointer-events: all;
  }