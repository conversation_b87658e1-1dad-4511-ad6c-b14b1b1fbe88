import React, { useRef } from 'react'
import { useGLTF } from '@react-three/drei'

export function Model(props) {
  const { nodes, materials } = useGLTF('/need_some_space/scene.gltf')
  return (
    <group {...props} dispose={null}>
      <points
        geometry={nodes.Object_2.geometry}
        material={materials['Scene_-_Root']}
        rotation={[-Math.PI / 2, 0, 0]}
        scale={0.013}
      />
    </group>
  )
}

useGLTF.preload('/need_some_space/scene.gltf')
