{"name": "@react-spring/types", "version": "10.0.2", "description": "Internal package with TypeScript stuff", "module": "./dist/react-spring_types.legacy-esm.js", "main": "./dist/cjs/index.js", "types": "./dist/react-spring_types.modern.d.mts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/react-spring_types.modern.d.mts", "default": "./dist/react-spring_types.modern.mjs"}, "require": {"types": "./dist/cjs/react-spring_types.development.d.ts", "default": "./dist/cjs/index.js"}}}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "git+https://github.com/pmndrs/react-spring.git"}, "homepage": "https://github.com/pmndrs/react-spring#readme", "keywords": ["animated", "animation", "hooks", "motion", "react", "react-native", "spring", "typescript", "velocity"], "license": "MIT", "author": "<PERSON>", "maintainers": ["<PERSON> (https://github.com/joshua<PERSON>s)"], "scripts": {"build": "tsup", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "dev": "tsup --watch", "lint": "TIMING=1 eslint \"src/**/*.ts*\"", "pack": "yarn pack"}}