/*
  @license
	Rollup.js v4.50.2
	Mon, 15 Sep 2025 07:13:55 GMT - commit 76a3b8ede4729a71eb522fc29f7d550a4358827b

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
export { version as VERSION, defineConfig, rollup, watch } from './shared/node-entry.js';
import './shared/parseAst.js';
import '../native.js';
import 'node:path';
import 'path';
import 'node:process';
import 'node:perf_hooks';
import 'node:fs/promises';
