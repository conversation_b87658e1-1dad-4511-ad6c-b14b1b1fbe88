{"version": 3, "file": "SSAARenderPass.cjs", "sources": ["../../src/postprocessing/SSAARenderPass.js"], "sourcesContent": ["import { AdditiveBlending, Color, HalfFloatType, ShaderMaterial, UniformsUtils, WebGLRenderTarget } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\n\n/**\n *\n * Supersample Anti-Aliasing Render Pass\n *\n * This manual approach to SSAA re-renders the scene ones for each sample with camera jitter and accumulates the results.\n *\n * References: https://en.wikipedia.org/wiki/Supersampling\n *\n */\n\nclass SSAARenderPass extends Pass {\n  constructor(scene, camera, clearColor, clearAlpha) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.sampleLevel = 4 // specified as n, where the number of samples is 2^n, so sampleLevel = 4, is 2^4 samples, 16.\n    this.unbiased = true\n\n    // as we need to clear the buffer in this pass, clearColor must be set to something, defaults to black.\n    this.clearColor = clearColor !== undefined ? clearColor : 0x000000\n    this.clearAlpha = clearAlpha !== undefined ? clearAlpha : 0\n    this._oldClearColor = new Color()\n\n    const copyShader = CopyShader\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms)\n\n    this.copyMaterial = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      transparent: true,\n      depthTest: false,\n      depthWrite: false,\n      premultipliedAlpha: true,\n      blending: AdditiveBlending,\n    })\n\n    this.fsQuad = new FullScreenQuad(this.copyMaterial)\n  }\n\n  dispose() {\n    if (this.sampleRenderTarget) {\n      this.sampleRenderTarget.dispose()\n      this.sampleRenderTarget = null\n    }\n\n    this.copyMaterial.dispose()\n\n    this.fsQuad.dispose()\n  }\n\n  setSize(width, height) {\n    if (this.sampleRenderTarget) this.sampleRenderTarget.setSize(width, height)\n  }\n\n  render(renderer, writeBuffer, readBuffer) {\n    if (!this.sampleRenderTarget) {\n      this.sampleRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, { type: HalfFloatType })\n      this.sampleRenderTarget.texture.name = 'SSAARenderPass.sample'\n    }\n\n    const jitterOffsets = _JitterVectors[Math.max(0, Math.min(this.sampleLevel, 5))]\n\n    const autoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    renderer.getClearColor(this._oldClearColor)\n    const oldClearAlpha = renderer.getClearAlpha()\n\n    const baseSampleWeight = 1.0 / jitterOffsets.length\n    const roundingRange = 1 / 32\n    this.copyUniforms['tDiffuse'].value = this.sampleRenderTarget.texture\n\n    const viewOffset = {\n      fullWidth: readBuffer.width,\n      fullHeight: readBuffer.height,\n      offsetX: 0,\n      offsetY: 0,\n      width: readBuffer.width,\n      height: readBuffer.height,\n    }\n\n    const originalViewOffset = Object.assign({}, this.camera.view)\n\n    if (originalViewOffset.enabled) Object.assign(viewOffset, originalViewOffset)\n\n    // render the scene multiple times, each slightly jitter offset from the last and accumulate the results.\n    for (let i = 0; i < jitterOffsets.length; i++) {\n      const jitterOffset = jitterOffsets[i]\n\n      if (this.camera.setViewOffset) {\n        this.camera.setViewOffset(\n          viewOffset.fullWidth,\n          viewOffset.fullHeight,\n\n          viewOffset.offsetX + jitterOffset[0] * 0.0625,\n          viewOffset.offsetY + jitterOffset[1] * 0.0625, // 0.0625 = 1 / 16\n\n          viewOffset.width,\n          viewOffset.height,\n        )\n      }\n\n      let sampleWeight = baseSampleWeight\n\n      if (this.unbiased) {\n        // the theory is that equal weights for each sample lead to an accumulation of rounding errors.\n        // The following equation varies the sampleWeight per sample so that it is uniformly distributed\n        // across a range of values whose rounding errors cancel each other out.\n\n        const uniformCenteredDistribution = -0.5 + (i + 0.5) / jitterOffsets.length\n        sampleWeight += roundingRange * uniformCenteredDistribution\n      }\n\n      this.copyUniforms['opacity'].value = sampleWeight\n      renderer.setClearColor(this.clearColor, this.clearAlpha)\n      renderer.setRenderTarget(this.sampleRenderTarget)\n      renderer.clear()\n      renderer.render(this.scene, this.camera)\n\n      renderer.setRenderTarget(this.renderToScreen ? null : writeBuffer)\n\n      if (i === 0) {\n        renderer.setClearColor(0x000000, 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.render(renderer)\n    }\n\n    if (this.camera.setViewOffset && originalViewOffset.enabled) {\n      this.camera.setViewOffset(\n        originalViewOffset.fullWidth,\n        originalViewOffset.fullHeight,\n\n        originalViewOffset.offsetX,\n        originalViewOffset.offsetY,\n\n        originalViewOffset.width,\n        originalViewOffset.height,\n      )\n    } else if (this.camera.clearViewOffset) {\n      this.camera.clearViewOffset()\n    }\n\n    renderer.autoClear = autoClear\n    renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n  }\n}\n\n// These jitter vectors are specified in integers because it is easier.\n// I am assuming a [-8,8) integer grid, but it needs to be mapped onto [-0.5,0.5)\n// before being used, thus these integers need to be scaled by 1/16.\n//\n// Sample patterns reference: https://msdn.microsoft.com/en-us/library/windows/desktop/ff476218%28v=vs.85%29.aspx?f=255&MSPPError=-2147217396\n// prettier-ignore\nconst _JitterVectors = [\n\t[\n\t\t[ 0, 0 ]\n\t],\n\t[\n\t\t[ 4, 4 ], [ - 4, - 4 ]\n\t],\n\t[\n\t\t[ - 2, - 6 ], [ 6, - 2 ], [ - 6, 2 ], [ 2, 6 ]\n\t],\n\t[\n\t\t[ 1, - 3 ], [ - 1, 3 ], [ 5, 1 ], [ - 3, - 5 ],\n\t\t[ - 5, 5 ], [ - 7, - 1 ], [ 3, 7 ], [ 7, - 7 ]\n\t],\n\t[\n\t\t[ 1, 1 ], [ - 1, - 3 ], [ - 3, 2 ], [ 4, - 1 ],\n\t\t[ - 5, - 2 ], [ 2, 5 ], [ 5, 3 ], [ 3, - 5 ],\n\t\t[ - 2, 6 ], [ 0, - 7 ], [ - 4, - 6 ], [ - 6, 4 ],\n\t\t[ - 8, 0 ], [ 7, - 4 ], [ 6, 7 ], [ - 7, - 8 ]\n\t],\n\t[\n\t\t[ - 4, - 7 ], [ - 7, - 5 ], [ - 3, - 5 ], [ - 5, - 4 ],\n\t\t[ - 1, - 4 ], [ - 2, - 2 ], [ - 6, - 1 ], [ - 4, 0 ],\n\t\t[ - 7, 1 ], [ - 1, 2 ], [ - 6, 3 ], [ - 3, 3 ],\n\t\t[ - 7, 6 ], [ - 3, 6 ], [ - 5, 7 ], [ - 1, 7 ],\n\t\t[ 5, - 7 ], [ 1, - 6 ], [ 6, - 5 ], [ 4, - 4 ],\n\t\t[ 2, - 3 ], [ 7, - 2 ], [ 1, - 1 ], [ 4, - 1 ],\n\t\t[ 2, 1 ], [ 6, 2 ], [ 0, 4 ], [ 4, 4 ],\n\t\t[ 2, 5 ], [ 7, 5 ], [ 5, 6 ], [ 3, 7 ]\n\t]\n];\n\nexport { SSAARenderPass }\n"], "names": ["Pass", "Color", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UniformsUtils", "ShaderMaterial", "AdditiveBlending", "FullScreenQuad", "WebGLRenderTarget", "HalfFloatType"], "mappings": ";;;;;AAcA,MAAM,uBAAuBA,KAAAA,KAAK;AAAA,EAChC,YAAY,OAAO,QAAQ,YAAY,YAAY;AACjD,UAAO;AAEP,SAAK,QAAQ;AACb,SAAK,SAAS;AAEd,SAAK,cAAc;AACnB,SAAK,WAAW;AAGhB,SAAK,aAAa,eAAe,SAAY,aAAa;AAC1D,SAAK,aAAa,eAAe,SAAY,aAAa;AAC1D,SAAK,iBAAiB,IAAIC,YAAO;AAEjC,UAAM,aAAaC,WAAU;AAC7B,SAAK,eAAeC,MAAAA,cAAc,MAAM,WAAW,QAAQ;AAE3D,SAAK,eAAe,IAAIC,qBAAe;AAAA,MACrC,UAAU,KAAK;AAAA,MACf,cAAc,WAAW;AAAA,MACzB,gBAAgB,WAAW;AAAA,MAC3B,aAAa;AAAA,MACb,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,UAAUC,MAAgB;AAAA,IAChC,CAAK;AAED,SAAK,SAAS,IAAIC,oBAAe,KAAK,YAAY;AAAA,EACnD;AAAA,EAED,UAAU;AACR,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB,QAAS;AACjC,WAAK,qBAAqB;AAAA,IAC3B;AAED,SAAK,aAAa,QAAS;AAE3B,SAAK,OAAO,QAAS;AAAA,EACtB;AAAA,EAED,QAAQ,OAAO,QAAQ;AACrB,QAAI,KAAK;AAAoB,WAAK,mBAAmB,QAAQ,OAAO,MAAM;AAAA,EAC3E;AAAA,EAED,OAAO,UAAU,aAAa,YAAY;AACxC,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,qBAAqB,IAAIC,MAAiB,kBAAC,WAAW,OAAO,WAAW,QAAQ,EAAE,MAAMC,MAAAA,eAAe;AAC5G,WAAK,mBAAmB,QAAQ,OAAO;AAAA,IACxC;AAED,UAAM,gBAAgB,eAAe,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC;AAE/E,UAAM,YAAY,SAAS;AAC3B,aAAS,YAAY;AAErB,aAAS,cAAc,KAAK,cAAc;AAC1C,UAAM,gBAAgB,SAAS,cAAe;AAE9C,UAAM,mBAAmB,IAAM,cAAc;AAC7C,UAAM,gBAAgB,IAAI;AAC1B,SAAK,aAAa,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AAE9D,UAAM,aAAa;AAAA,MACjB,WAAW,WAAW;AAAA,MACtB,YAAY,WAAW;AAAA,MACvB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO,WAAW;AAAA,MAClB,QAAQ,WAAW;AAAA,IACpB;AAED,UAAM,qBAAqB,OAAO,OAAO,CAAE,GAAE,KAAK,OAAO,IAAI;AAE7D,QAAI,mBAAmB;AAAS,aAAO,OAAO,YAAY,kBAAkB;AAG5E,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,YAAM,eAAe,cAAc,CAAC;AAEpC,UAAI,KAAK,OAAO,eAAe;AAC7B,aAAK,OAAO;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UAEX,WAAW,UAAU,aAAa,CAAC,IAAI;AAAA,UACvC,WAAW,UAAU,aAAa,CAAC,IAAI;AAAA;AAAA,UAEvC,WAAW;AAAA,UACX,WAAW;AAAA,QACZ;AAAA,MACF;AAED,UAAI,eAAe;AAEnB,UAAI,KAAK,UAAU;AAKjB,cAAM,8BAA8B,QAAQ,IAAI,OAAO,cAAc;AACrE,wBAAgB,gBAAgB;AAAA,MACjC;AAED,WAAK,aAAa,SAAS,EAAE,QAAQ;AACrC,eAAS,cAAc,KAAK,YAAY,KAAK,UAAU;AACvD,eAAS,gBAAgB,KAAK,kBAAkB;AAChD,eAAS,MAAO;AAChB,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAEvC,eAAS,gBAAgB,KAAK,iBAAiB,OAAO,WAAW;AAEjE,UAAI,MAAM,GAAG;AACX,iBAAS,cAAc,GAAU,CAAG;AACpC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,OAAO,OAAO,QAAQ;AAAA,IAC5B;AAED,QAAI,KAAK,OAAO,iBAAiB,mBAAmB,SAAS;AAC3D,WAAK,OAAO;AAAA,QACV,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QAEnB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QAEnB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACpB;AAAA,IACP,WAAe,KAAK,OAAO,iBAAiB;AACtC,WAAK,OAAO,gBAAiB;AAAA,IAC9B;AAED,aAAS,YAAY;AACrB,aAAS,cAAc,KAAK,gBAAgB,aAAa;AAAA,EAC1D;AACH;AAQA,MAAM,iBAAiB;AAAA,EACtB;AAAA,IACC,CAAE,GAAG,CAAG;AAAA,EACR;AAAA,EACD;AAAA,IACC,CAAE,GAAG,CAAG;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,EACtB;AAAA,EACD;AAAA,IACC,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,GAAG;IAAO,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,GAAG,CAAG;AAAA,EAC9C;AAAA,EACD;AAAA,IACC,CAAE,GAAG,EAAK;AAAA,IAAE,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,IAC9C,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,IAAK;IAAO,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,EAC9C;AAAA,EACD;AAAA,IACC,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC9C,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,GAAG;IAAK,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC5C,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,CAAG;AAAA,IAChD,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,EAC9C;AAAA,EACD;AAAA,IACC,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,EAAK;AAAA,IACtD,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,CAAG;AAAA,IACpD,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAG;AAAA,IAC9C,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAG;AAAA,IAC9C,CAAE,GAAG,EAAK;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC9C,CAAE,GAAG,EAAK;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC9C,CAAE,GAAG;IAAK,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,IACtC,CAAE,GAAG;IAAK,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,EACtC;AACF;;"}