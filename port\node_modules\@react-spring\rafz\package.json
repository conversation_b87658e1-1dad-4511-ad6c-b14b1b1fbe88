{"name": "@react-spring/rafz", "version": "10.0.2", "description": "react-spring's fork of rafz one frameloop to rule them all", "module": "./dist/react-spring_rafz.legacy-esm.js", "main": "./dist/cjs/index.js", "types": "./dist/react-spring_rafz.modern.d.mts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/react-spring_rafz.modern.d.mts", "default": "./dist/react-spring_rafz.modern.mjs"}, "require": {"types": "./dist/cjs/react-spring_rafz.development.d.ts", "default": "./dist/cjs/index.js"}}}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "git+https://github.com/pmndrs/react-spring.git"}, "homepage": "https://github.com/pmndrs/react-spring/tree/main/packages/rafz#readme", "keywords": ["animated", "animation", "hooks", "motion", "react", "react-native", "spring", "typescript", "velocity"], "license": "MIT", "author": "<PERSON>", "scripts": {"build": "tsup", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "dev": "tsup --watch", "lint": "TIMING=1 eslint \"src/**/*.ts*\"", "pack": "yarn pack"}}