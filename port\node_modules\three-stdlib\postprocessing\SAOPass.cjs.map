{"version": 3, "file": "SAOPass.cjs", "sources": ["../../src/postprocessing/SAOPass.js"], "sourcesContent": ["import {\n  AddEquation,\n  Color,\n  CustomBlending,\n  DepthTexture,\n  DstAlphaFactor,\n  DstColorFactor,\n  HalfFloatType,\n  MeshDepthMaterial,\n  MeshNormalMaterial,\n  NearestFilter,\n  NoBlending,\n  RGBADepthPacking,\n  ShaderMaterial,\n  UniformsUtils,\n  UnsignedShortType,\n  Vector2,\n  WebGLR<PERSON>Target,\n  ZeroFactor,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { SAOShader } from '../shaders/SAOShader'\nimport { DepthLimitedBlurShader } from '../shaders/DepthLimitedBlurShader'\nimport { BlurShaderUtils } from '../shaders/DepthLimitedBlurShader'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { UnpackDepthRGBAShader } from '../shaders/UnpackDepthRGBAShader'\n\n/**\n * SAO implementation inspired from bhouston previous SAO work\n */\nconst SAOPass = /* @__PURE__ */ (() => {\n  class SAOPass extends Pass {\n    static OUTPUT = {\n      Beauty: 1,\n      Default: 0,\n      SAO: 2,\n      Depth: 3,\n      Normal: 4,\n    }\n\n    constructor(scene, camera, useDepthTexture = false, useNormals = false, resolution = new Vector2(256, 256)) {\n      super()\n\n      this.scene = scene\n      this.camera = camera\n\n      this.clear = true\n      this.needsSwap = false\n\n      this.supportsDepthTextureExtension = useDepthTexture\n      this.supportsNormalTexture = useNormals\n\n      this.originalClearColor = new Color()\n      this._oldClearColor = new Color()\n      this.oldClearAlpha = 1\n\n      this.params = {\n        output: 0,\n        saoBias: 0.5,\n        saoIntensity: 0.18,\n        saoScale: 1,\n        saoKernelRadius: 100,\n        saoMinResolution: 0,\n        saoBlur: true,\n        saoBlurRadius: 8,\n        saoBlurStdDev: 4,\n        saoBlurDepthCutoff: 0.01,\n      }\n\n      this.resolution = new Vector2(resolution.x, resolution.y)\n\n      this.saoRenderTarget = new WebGLRenderTarget(this.resolution.x, this.resolution.y, { type: HalfFloatType })\n      this.blurIntermediateRenderTarget = this.saoRenderTarget.clone()\n      this.beautyRenderTarget = this.saoRenderTarget.clone()\n\n      this.normalRenderTarget = new WebGLRenderTarget(this.resolution.x, this.resolution.y, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n      })\n      this.depthRenderTarget = this.normalRenderTarget.clone()\n\n      let depthTexture\n\n      if (this.supportsDepthTextureExtension) {\n        depthTexture = new DepthTexture()\n        depthTexture.type = UnsignedShortType\n\n        this.beautyRenderTarget.depthTexture = depthTexture\n        this.beautyRenderTarget.depthBuffer = true\n      }\n\n      this.depthMaterial = new MeshDepthMaterial()\n      this.depthMaterial.depthPacking = RGBADepthPacking\n      this.depthMaterial.blending = NoBlending\n\n      this.normalMaterial = new MeshNormalMaterial()\n      this.normalMaterial.blending = NoBlending\n\n      this.saoMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SAOShader.defines),\n        fragmentShader: SAOShader.fragmentShader,\n        vertexShader: SAOShader.vertexShader,\n        uniforms: UniformsUtils.clone(SAOShader.uniforms),\n      })\n      this.saoMaterial.extensions.derivatives = true\n      this.saoMaterial.defines['DEPTH_PACKING'] = this.supportsDepthTextureExtension ? 0 : 1\n      this.saoMaterial.defines['NORMAL_TEXTURE'] = this.supportsNormalTexture ? 1 : 0\n      this.saoMaterial.defines['PERSPECTIVE_CAMERA'] = this.camera.isPerspectiveCamera ? 1 : 0\n      this.saoMaterial.uniforms['tDepth'].value = this.supportsDepthTextureExtension\n        ? depthTexture\n        : this.depthRenderTarget.texture\n      this.saoMaterial.uniforms['tNormal'].value = this.normalRenderTarget.texture\n      this.saoMaterial.uniforms['size'].value.set(this.resolution.x, this.resolution.y)\n      this.saoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n      this.saoMaterial.uniforms['cameraProjectionMatrix'].value = this.camera.projectionMatrix\n      this.saoMaterial.blending = NoBlending\n\n      this.vBlurMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(DepthLimitedBlurShader.uniforms),\n        defines: Object.assign({}, DepthLimitedBlurShader.defines),\n        vertexShader: DepthLimitedBlurShader.vertexShader,\n        fragmentShader: DepthLimitedBlurShader.fragmentShader,\n      })\n      this.vBlurMaterial.defines['DEPTH_PACKING'] = this.supportsDepthTextureExtension ? 0 : 1\n      this.vBlurMaterial.defines['PERSPECTIVE_CAMERA'] = this.camera.isPerspectiveCamera ? 1 : 0\n      this.vBlurMaterial.uniforms['tDiffuse'].value = this.saoRenderTarget.texture\n      this.vBlurMaterial.uniforms['tDepth'].value = this.supportsDepthTextureExtension\n        ? depthTexture\n        : this.depthRenderTarget.texture\n      this.vBlurMaterial.uniforms['size'].value.set(this.resolution.x, this.resolution.y)\n      this.vBlurMaterial.blending = NoBlending\n\n      this.hBlurMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(DepthLimitedBlurShader.uniforms),\n        defines: Object.assign({}, DepthLimitedBlurShader.defines),\n        vertexShader: DepthLimitedBlurShader.vertexShader,\n        fragmentShader: DepthLimitedBlurShader.fragmentShader,\n      })\n      this.hBlurMaterial.defines['DEPTH_PACKING'] = this.supportsDepthTextureExtension ? 0 : 1\n      this.hBlurMaterial.defines['PERSPECTIVE_CAMERA'] = this.camera.isPerspectiveCamera ? 1 : 0\n      this.hBlurMaterial.uniforms['tDiffuse'].value = this.blurIntermediateRenderTarget.texture\n      this.hBlurMaterial.uniforms['tDepth'].value = this.supportsDepthTextureExtension\n        ? depthTexture\n        : this.depthRenderTarget.texture\n      this.hBlurMaterial.uniforms['size'].value.set(this.resolution.x, this.resolution.y)\n      this.hBlurMaterial.blending = NoBlending\n\n      this.materialCopy = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        blending: NoBlending,\n      })\n      this.materialCopy.transparent = true\n      this.materialCopy.depthTest = false\n      this.materialCopy.depthWrite = false\n      this.materialCopy.blending = CustomBlending\n      this.materialCopy.blendSrc = DstColorFactor\n      this.materialCopy.blendDst = ZeroFactor\n      this.materialCopy.blendEquation = AddEquation\n      this.materialCopy.blendSrcAlpha = DstAlphaFactor\n      this.materialCopy.blendDstAlpha = ZeroFactor\n      this.materialCopy.blendEquationAlpha = AddEquation\n\n      this.depthCopy = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(UnpackDepthRGBAShader.uniforms),\n        vertexShader: UnpackDepthRGBAShader.vertexShader,\n        fragmentShader: UnpackDepthRGBAShader.fragmentShader,\n        blending: NoBlending,\n      })\n\n      this.fsQuad = new FullScreenQuad(null)\n    }\n\n    render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive*/) {\n      // Rendering readBuffer first when rendering to screen\n      if (this.renderToScreen) {\n        this.materialCopy.blending = NoBlending\n        this.materialCopy.uniforms['tDiffuse'].value = readBuffer.texture\n        this.materialCopy.needsUpdate = true\n        this.renderPass(renderer, this.materialCopy, null)\n      }\n\n      if (this.params.output === 1) {\n        return\n      }\n\n      renderer.getClearColor(this._oldClearColor)\n      this.oldClearAlpha = renderer.getClearAlpha()\n      const oldAutoClear = renderer.autoClear\n      renderer.autoClear = false\n\n      renderer.setRenderTarget(this.depthRenderTarget)\n      renderer.clear()\n\n      this.saoMaterial.uniforms['bias'].value = this.params.saoBias\n      this.saoMaterial.uniforms['intensity'].value = this.params.saoIntensity\n      this.saoMaterial.uniforms['scale'].value = this.params.saoScale\n      this.saoMaterial.uniforms['kernelRadius'].value = this.params.saoKernelRadius\n      this.saoMaterial.uniforms['minResolution'].value = this.params.saoMinResolution\n      this.saoMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.saoMaterial.uniforms['cameraFar'].value = this.camera.far\n      // this.saoMaterial.uniforms['randomSeed'].value = Math.random();\n\n      const depthCutoff = this.params.saoBlurDepthCutoff * (this.camera.far - this.camera.near)\n      this.vBlurMaterial.uniforms['depthCutoff'].value = depthCutoff\n      this.hBlurMaterial.uniforms['depthCutoff'].value = depthCutoff\n\n      this.vBlurMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.vBlurMaterial.uniforms['cameraFar'].value = this.camera.far\n      this.hBlurMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.hBlurMaterial.uniforms['cameraFar'].value = this.camera.far\n\n      this.params.saoBlurRadius = Math.floor(this.params.saoBlurRadius)\n      if (this.prevStdDev !== this.params.saoBlurStdDev || this.prevNumSamples !== this.params.saoBlurRadius) {\n        BlurShaderUtils.configure(\n          this.vBlurMaterial,\n          this.params.saoBlurRadius,\n          this.params.saoBlurStdDev,\n          new Vector2(0, 1),\n        )\n        BlurShaderUtils.configure(\n          this.hBlurMaterial,\n          this.params.saoBlurRadius,\n          this.params.saoBlurStdDev,\n          new Vector2(1, 0),\n        )\n        this.prevStdDev = this.params.saoBlurStdDev\n        this.prevNumSamples = this.params.saoBlurRadius\n      }\n\n      // Rendering scene to depth texture\n      renderer.setClearColor(0x000000)\n      renderer.setRenderTarget(this.beautyRenderTarget)\n      renderer.clear()\n      renderer.render(this.scene, this.camera)\n\n      // Re-render scene if depth texture extension is not supported\n      if (!this.supportsDepthTextureExtension) {\n        // Clear rule : far clipping plane in both RGBA and Basic encoding\n        this.renderOverride(renderer, this.depthMaterial, this.depthRenderTarget, 0x000000, 1.0)\n      }\n\n      if (this.supportsNormalTexture) {\n        // Clear rule : default normal is facing the camera\n        this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 0x7777ff, 1.0)\n      }\n\n      // Rendering SAO texture\n      this.renderPass(renderer, this.saoMaterial, this.saoRenderTarget, 0xffffff, 1.0)\n\n      // Blurring SAO texture\n      if (this.params.saoBlur) {\n        this.renderPass(renderer, this.vBlurMaterial, this.blurIntermediateRenderTarget, 0xffffff, 1.0)\n        this.renderPass(renderer, this.hBlurMaterial, this.saoRenderTarget, 0xffffff, 1.0)\n      }\n\n      let outputMaterial = this.materialCopy\n      // Setting up SAO rendering\n      if (this.params.output === 3) {\n        if (this.supportsDepthTextureExtension) {\n          this.materialCopy.uniforms['tDiffuse'].value = this.beautyRenderTarget.depthTexture\n          this.materialCopy.needsUpdate = true\n        } else {\n          this.depthCopy.uniforms['tDiffuse'].value = this.depthRenderTarget.texture\n          this.depthCopy.needsUpdate = true\n          outputMaterial = this.depthCopy\n        }\n      } else if (this.params.output === 4) {\n        this.materialCopy.uniforms['tDiffuse'].value = this.normalRenderTarget.texture\n        this.materialCopy.needsUpdate = true\n      } else {\n        this.materialCopy.uniforms['tDiffuse'].value = this.saoRenderTarget.texture\n        this.materialCopy.needsUpdate = true\n      }\n\n      // Blending depends on output, only want a CustomBlending when showing SAO\n      if (this.params.output === 0) {\n        outputMaterial.blending = CustomBlending\n      } else {\n        outputMaterial.blending = NoBlending\n      }\n\n      // Rendering SAOPass result on top of previous pass\n      this.renderPass(renderer, outputMaterial, this.renderToScreen ? null : readBuffer)\n\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha)\n      renderer.autoClear = oldAutoClear\n    }\n\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      // save original state\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n\n      // setup pass state\n      renderer.autoClear = false\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.material = passMaterial\n      this.fsQuad.render(renderer)\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.overrideMaterial = overrideMaterial\n      renderer.render(this.scene, this.camera)\n      this.scene.overrideMaterial = null\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    setSize(width, height) {\n      this.beautyRenderTarget.setSize(width, height)\n      this.saoRenderTarget.setSize(width, height)\n      this.blurIntermediateRenderTarget.setSize(width, height)\n      this.normalRenderTarget.setSize(width, height)\n      this.depthRenderTarget.setSize(width, height)\n\n      this.saoMaterial.uniforms['size'].value.set(width, height)\n      this.saoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n      this.saoMaterial.uniforms['cameraProjectionMatrix'].value = this.camera.projectionMatrix\n      this.saoMaterial.needsUpdate = true\n\n      this.vBlurMaterial.uniforms['size'].value.set(width, height)\n      this.vBlurMaterial.needsUpdate = true\n\n      this.hBlurMaterial.uniforms['size'].value.set(width, height)\n      this.hBlurMaterial.needsUpdate = true\n    }\n\n    dispose() {\n      this.saoRenderTarget.dispose()\n      this.blurIntermediateRenderTarget.dispose()\n      this.beautyRenderTarget.dispose()\n      this.normalRenderTarget.dispose()\n      this.depthRenderTarget.dispose()\n\n      this.depthMaterial.dispose()\n      this.normalMaterial.dispose()\n      this.saoMaterial.dispose()\n      this.vBlurMaterial.dispose()\n      this.hBlurMaterial.dispose()\n      this.materialCopy.dispose()\n      this.depthCopy.dispose()\n\n      this.fsQuad.dispose()\n    }\n  }\n\n  return SAOPass\n})()\n\nexport { SAOPass }\n"], "names": ["SAOPass", "Pass", "Vector2", "Color", "WebGLRenderTarget", "HalfFloatType", "NearestFilter", "DepthTexture", "UnsignedShortType", "MeshDepthMaterial", "RGBADepthPacking", "NoBlending", "MeshNormalMaterial", "ShaderMaterial", "SAOShader", "UniformsUtils", "DepthLimited<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CustomBlending", "DstColorFactor", "ZeroFactor", "AddEquation", "DstAlphaFactor", "UnpackDepthRGBAShader", "FullScreenQuad", "<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;AA8BK,MAAC,UAA2B,uBAAM;AACrC,QAAMA,iBAAgBC,KAAAA,KAAK;AAAA,IASzB,YAAY,OAAO,QAAQ,kBAAkB,OAAO,aAAa,OAAO,aAAa,IAAIC,MAAAA,QAAQ,KAAK,GAAG,GAAG;AAC1G,YAAO;AAEP,WAAK,QAAQ;AACb,WAAK,SAAS;AAEd,WAAK,QAAQ;AACb,WAAK,YAAY;AAEjB,WAAK,gCAAgC;AACrC,WAAK,wBAAwB;AAE7B,WAAK,qBAAqB,IAAIC,YAAO;AACrC,WAAK,iBAAiB,IAAIA,YAAO;AACjC,WAAK,gBAAgB;AAErB,WAAK,SAAS;AAAA,QACZ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,cAAc;AAAA,QACd,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,eAAe;AAAA,QACf,eAAe;AAAA,QACf,oBAAoB;AAAA,MACrB;AAED,WAAK,aAAa,IAAID,MAAO,QAAC,WAAW,GAAG,WAAW,CAAC;AAExD,WAAK,kBAAkB,IAAIE,wBAAkB,KAAK,WAAW,GAAG,KAAK,WAAW,GAAG,EAAE,MAAMC,MAAa,cAAA,CAAE;AAC1G,WAAK,+BAA+B,KAAK,gBAAgB,MAAO;AAChE,WAAK,qBAAqB,KAAK,gBAAgB,MAAO;AAEtD,WAAK,qBAAqB,IAAID,MAAAA,kBAAkB,KAAK,WAAW,GAAG,KAAK,WAAW,GAAG;AAAA,QACpF,WAAWE,MAAa;AAAA,QACxB,WAAWA,MAAa;AAAA,QACxB,MAAMD,MAAa;AAAA,MAC3B,CAAO;AACD,WAAK,oBAAoB,KAAK,mBAAmB,MAAO;AAExD,UAAI;AAEJ,UAAI,KAAK,+BAA+B;AACtC,uBAAe,IAAIE,MAAAA,aAAc;AACjC,qBAAa,OAAOC,MAAiB;AAErC,aAAK,mBAAmB,eAAe;AACvC,aAAK,mBAAmB,cAAc;AAAA,MACvC;AAED,WAAK,gBAAgB,IAAIC,wBAAmB;AAC5C,WAAK,cAAc,eAAeC,MAAgB;AAClD,WAAK,cAAc,WAAWC,MAAU;AAExC,WAAK,iBAAiB,IAAIC,yBAAoB;AAC9C,WAAK,eAAe,WAAWD,MAAU;AAEzC,WAAK,cAAc,IAAIE,qBAAe;AAAA,QACpC,SAAS,OAAO,OAAO,CAAA,GAAIC,UAAAA,UAAU,OAAO;AAAA,QAC5C,gBAAgBA,UAAS,UAAC;AAAA,QAC1B,cAAcA,UAAS,UAAC;AAAA,QACxB,UAAUC,MAAa,cAAC,MAAMD,UAAAA,UAAU,QAAQ;AAAA,MACxD,CAAO;AACD,WAAK,YAAY,WAAW,cAAc;AAC1C,WAAK,YAAY,QAAQ,eAAe,IAAI,KAAK,gCAAgC,IAAI;AACrF,WAAK,YAAY,QAAQ,gBAAgB,IAAI,KAAK,wBAAwB,IAAI;AAC9E,WAAK,YAAY,QAAQ,oBAAoB,IAAI,KAAK,OAAO,sBAAsB,IAAI;AACvF,WAAK,YAAY,SAAS,QAAQ,EAAE,QAAQ,KAAK,gCAC7C,eACA,KAAK,kBAAkB;AAC3B,WAAK,YAAY,SAAS,SAAS,EAAE,QAAQ,KAAK,mBAAmB;AACrE,WAAK,YAAY,SAAS,MAAM,EAAE,MAAM,IAAI,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC;AAChF,WAAK,YAAY,SAAS,+BAA+B,EAAE,MAAM,KAAK,KAAK,OAAO,uBAAuB;AACzG,WAAK,YAAY,SAAS,wBAAwB,EAAE,QAAQ,KAAK,OAAO;AACxE,WAAK,YAAY,WAAWH,MAAU;AAEtC,WAAK,gBAAgB,IAAIE,qBAAe;AAAA,QACtC,UAAUE,MAAa,cAAC,MAAMC,uBAAAA,uBAAuB,QAAQ;AAAA,QAC7D,SAAS,OAAO,OAAO,CAAA,GAAIA,uBAAAA,uBAAuB,OAAO;AAAA,QACzD,cAAcA,uBAAsB,uBAAC;AAAA,QACrC,gBAAgBA,uBAAsB,uBAAC;AAAA,MAC/C,CAAO;AACD,WAAK,cAAc,QAAQ,eAAe,IAAI,KAAK,gCAAgC,IAAI;AACvF,WAAK,cAAc,QAAQ,oBAAoB,IAAI,KAAK,OAAO,sBAAsB,IAAI;AACzF,WAAK,cAAc,SAAS,UAAU,EAAE,QAAQ,KAAK,gBAAgB;AACrE,WAAK,cAAc,SAAS,QAAQ,EAAE,QAAQ,KAAK,gCAC/C,eACA,KAAK,kBAAkB;AAC3B,WAAK,cAAc,SAAS,MAAM,EAAE,MAAM,IAAI,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC;AAClF,WAAK,cAAc,WAAWL,MAAU;AAExC,WAAK,gBAAgB,IAAIE,qBAAe;AAAA,QACtC,UAAUE,MAAa,cAAC,MAAMC,uBAAAA,uBAAuB,QAAQ;AAAA,QAC7D,SAAS,OAAO,OAAO,CAAA,GAAIA,uBAAAA,uBAAuB,OAAO;AAAA,QACzD,cAAcA,uBAAsB,uBAAC;AAAA,QACrC,gBAAgBA,uBAAsB,uBAAC;AAAA,MAC/C,CAAO;AACD,WAAK,cAAc,QAAQ,eAAe,IAAI,KAAK,gCAAgC,IAAI;AACvF,WAAK,cAAc,QAAQ,oBAAoB,IAAI,KAAK,OAAO,sBAAsB,IAAI;AACzF,WAAK,cAAc,SAAS,UAAU,EAAE,QAAQ,KAAK,6BAA6B;AAClF,WAAK,cAAc,SAAS,QAAQ,EAAE,QAAQ,KAAK,gCAC/C,eACA,KAAK,kBAAkB;AAC3B,WAAK,cAAc,SAAS,MAAM,EAAE,MAAM,IAAI,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC;AAClF,WAAK,cAAc,WAAWL,MAAU;AAExC,WAAK,eAAe,IAAIE,qBAAe;AAAA,QACrC,UAAUE,MAAa,cAAC,MAAME,WAAAA,WAAW,QAAQ;AAAA,QACjD,cAAcA,WAAU,WAAC;AAAA,QACzB,gBAAgBA,WAAU,WAAC;AAAA,QAC3B,UAAUN,MAAU;AAAA,MAC5B,CAAO;AACD,WAAK,aAAa,cAAc;AAChC,WAAK,aAAa,YAAY;AAC9B,WAAK,aAAa,aAAa;AAC/B,WAAK,aAAa,WAAWO,MAAc;AAC3C,WAAK,aAAa,WAAWC,MAAc;AAC3C,WAAK,aAAa,WAAWC,MAAU;AACvC,WAAK,aAAa,gBAAgBC,MAAW;AAC7C,WAAK,aAAa,gBAAgBC,MAAc;AAChD,WAAK,aAAa,gBAAgBF,MAAU;AAC5C,WAAK,aAAa,qBAAqBC,MAAW;AAElD,WAAK,YAAY,IAAIR,qBAAe;AAAA,QAClC,UAAUE,MAAa,cAAC,MAAMQ,sBAAAA,sBAAsB,QAAQ;AAAA,QAC5D,cAAcA,sBAAqB,sBAAC;AAAA,QACpC,gBAAgBA,sBAAqB,sBAAC;AAAA,QACtC,UAAUZ,MAAU;AAAA,MAC5B,CAAO;AAED,WAAK,SAAS,IAAIa,KAAc,eAAC,IAAI;AAAA,IACtC;AAAA,IAED,OAAO,UAAU,aAAa,YAAwC;AAEpE,UAAI,KAAK,gBAAgB;AACvB,aAAK,aAAa,WAAWb,MAAU;AACvC,aAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,WAAW;AAC1D,aAAK,aAAa,cAAc;AAChC,aAAK,WAAW,UAAU,KAAK,cAAc,IAAI;AAAA,MAClD;AAED,UAAI,KAAK,OAAO,WAAW,GAAG;AAC5B;AAAA,MACD;AAED,eAAS,cAAc,KAAK,cAAc;AAC1C,WAAK,gBAAgB,SAAS,cAAe;AAC7C,YAAM,eAAe,SAAS;AAC9B,eAAS,YAAY;AAErB,eAAS,gBAAgB,KAAK,iBAAiB;AAC/C,eAAS,MAAO;AAEhB,WAAK,YAAY,SAAS,MAAM,EAAE,QAAQ,KAAK,OAAO;AACtD,WAAK,YAAY,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAC3D,WAAK,YAAY,SAAS,OAAO,EAAE,QAAQ,KAAK,OAAO;AACvD,WAAK,YAAY,SAAS,cAAc,EAAE,QAAQ,KAAK,OAAO;AAC9D,WAAK,YAAY,SAAS,eAAe,EAAE,QAAQ,KAAK,OAAO;AAC/D,WAAK,YAAY,SAAS,YAAY,EAAE,QAAQ,KAAK,OAAO;AAC5D,WAAK,YAAY,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAG3D,YAAM,cAAc,KAAK,OAAO,sBAAsB,KAAK,OAAO,MAAM,KAAK,OAAO;AACpF,WAAK,cAAc,SAAS,aAAa,EAAE,QAAQ;AACnD,WAAK,cAAc,SAAS,aAAa,EAAE,QAAQ;AAEnD,WAAK,cAAc,SAAS,YAAY,EAAE,QAAQ,KAAK,OAAO;AAC9D,WAAK,cAAc,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAC7D,WAAK,cAAc,SAAS,YAAY,EAAE,QAAQ,KAAK,OAAO;AAC9D,WAAK,cAAc,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAE7D,WAAK,OAAO,gBAAgB,KAAK,MAAM,KAAK,OAAO,aAAa;AAChE,UAAI,KAAK,eAAe,KAAK,OAAO,iBAAiB,KAAK,mBAAmB,KAAK,OAAO,eAAe;AACtGc,+BAAAA,gBAAgB;AAAA,UACd,KAAK;AAAA,UACL,KAAK,OAAO;AAAA,UACZ,KAAK,OAAO;AAAA,UACZ,IAAIvB,MAAO,QAAC,GAAG,CAAC;AAAA,QACjB;AACDuB,+BAAAA,gBAAgB;AAAA,UACd,KAAK;AAAA,UACL,KAAK,OAAO;AAAA,UACZ,KAAK,OAAO;AAAA,UACZ,IAAIvB,MAAO,QAAC,GAAG,CAAC;AAAA,QACjB;AACD,aAAK,aAAa,KAAK,OAAO;AAC9B,aAAK,iBAAiB,KAAK,OAAO;AAAA,MACnC;AAGD,eAAS,cAAc,CAAQ;AAC/B,eAAS,gBAAgB,KAAK,kBAAkB;AAChD,eAAS,MAAO;AAChB,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAGvC,UAAI,CAAC,KAAK,+BAA+B;AAEvC,aAAK,eAAe,UAAU,KAAK,eAAe,KAAK,mBAAmB,GAAU,CAAG;AAAA,MACxF;AAED,UAAI,KAAK,uBAAuB;AAE9B,aAAK,eAAe,UAAU,KAAK,gBAAgB,KAAK,oBAAoB,SAAU,CAAG;AAAA,MAC1F;AAGD,WAAK,WAAW,UAAU,KAAK,aAAa,KAAK,iBAAiB,UAAU,CAAG;AAG/E,UAAI,KAAK,OAAO,SAAS;AACvB,aAAK,WAAW,UAAU,KAAK,eAAe,KAAK,8BAA8B,UAAU,CAAG;AAC9F,aAAK,WAAW,UAAU,KAAK,eAAe,KAAK,iBAAiB,UAAU,CAAG;AAAA,MAClF;AAED,UAAI,iBAAiB,KAAK;AAE1B,UAAI,KAAK,OAAO,WAAW,GAAG;AAC5B,YAAI,KAAK,+BAA+B;AACtC,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,eAAK,aAAa,cAAc;AAAA,QAC1C,OAAe;AACL,eAAK,UAAU,SAAS,UAAU,EAAE,QAAQ,KAAK,kBAAkB;AACnE,eAAK,UAAU,cAAc;AAC7B,2BAAiB,KAAK;AAAA,QACvB;AAAA,MACF,WAAU,KAAK,OAAO,WAAW,GAAG;AACnC,aAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,aAAK,aAAa,cAAc;AAAA,MACxC,OAAa;AACL,aAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,gBAAgB;AACpE,aAAK,aAAa,cAAc;AAAA,MACjC;AAGD,UAAI,KAAK,OAAO,WAAW,GAAG;AAC5B,uBAAe,WAAWgB,MAAc;AAAA,MAChD,OAAa;AACL,uBAAe,WAAWP,MAAU;AAAA,MACrC;AAGD,WAAK,WAAW,UAAU,gBAAgB,KAAK,iBAAiB,OAAO,UAAU;AAEjF,eAAS,cAAc,KAAK,gBAAgB,KAAK,aAAa;AAC9D,eAAS,YAAY;AAAA,IACtB;AAAA,IAED,WAAW,UAAU,cAAc,cAAc,YAAY,YAAY;AAEvE,eAAS,cAAc,KAAK,kBAAkB;AAC9C,YAAM,qBAAqB,SAAS,cAAe;AACnD,YAAM,oBAAoB,SAAS;AAEnC,eAAS,gBAAgB,YAAY;AAGrC,eAAS,YAAY;AACrB,UAAI,eAAe,UAAa,eAAe,MAAM;AACnD,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,cAAc,CAAG;AACxC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,OAAO,WAAW;AACvB,WAAK,OAAO,OAAO,QAAQ;AAG3B,eAAS,YAAY;AACrB,eAAS,cAAc,KAAK,kBAAkB;AAC9C,eAAS,cAAc,kBAAkB;AAAA,IAC1C;AAAA,IAED,eAAe,UAAU,kBAAkB,cAAc,YAAY,YAAY;AAC/E,eAAS,cAAc,KAAK,kBAAkB;AAC9C,YAAM,qBAAqB,SAAS,cAAe;AACnD,YAAM,oBAAoB,SAAS;AAEnC,eAAS,gBAAgB,YAAY;AACrC,eAAS,YAAY;AAErB,mBAAa,iBAAiB,cAAc;AAC5C,mBAAa,iBAAiB,cAAc;AAC5C,UAAI,eAAe,UAAa,eAAe,MAAM;AACnD,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,cAAc,CAAG;AACxC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,MAAM,mBAAmB;AAC9B,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AACvC,WAAK,MAAM,mBAAmB;AAG9B,eAAS,YAAY;AACrB,eAAS,cAAc,KAAK,kBAAkB;AAC9C,eAAS,cAAc,kBAAkB;AAAA,IAC1C;AAAA,IAED,QAAQ,OAAO,QAAQ;AACrB,WAAK,mBAAmB,QAAQ,OAAO,MAAM;AAC7C,WAAK,gBAAgB,QAAQ,OAAO,MAAM;AAC1C,WAAK,6BAA6B,QAAQ,OAAO,MAAM;AACvD,WAAK,mBAAmB,QAAQ,OAAO,MAAM;AAC7C,WAAK,kBAAkB,QAAQ,OAAO,MAAM;AAE5C,WAAK,YAAY,SAAS,MAAM,EAAE,MAAM,IAAI,OAAO,MAAM;AACzD,WAAK,YAAY,SAAS,+BAA+B,EAAE,MAAM,KAAK,KAAK,OAAO,uBAAuB;AACzG,WAAK,YAAY,SAAS,wBAAwB,EAAE,QAAQ,KAAK,OAAO;AACxE,WAAK,YAAY,cAAc;AAE/B,WAAK,cAAc,SAAS,MAAM,EAAE,MAAM,IAAI,OAAO,MAAM;AAC3D,WAAK,cAAc,cAAc;AAEjC,WAAK,cAAc,SAAS,MAAM,EAAE,MAAM,IAAI,OAAO,MAAM;AAC3D,WAAK,cAAc,cAAc;AAAA,IAClC;AAAA,IAED,UAAU;AACR,WAAK,gBAAgB,QAAS;AAC9B,WAAK,6BAA6B,QAAS;AAC3C,WAAK,mBAAmB,QAAS;AACjC,WAAK,mBAAmB,QAAS;AACjC,WAAK,kBAAkB,QAAS;AAEhC,WAAK,cAAc,QAAS;AAC5B,WAAK,eAAe,QAAS;AAC7B,WAAK,YAAY,QAAS;AAC1B,WAAK,cAAc,QAAS;AAC5B,WAAK,cAAc,QAAS;AAC5B,WAAK,aAAa,QAAS;AAC3B,WAAK,UAAU,QAAS;AAExB,WAAK,OAAO,QAAS;AAAA,IACtB;AAAA,EACF;AA1VC,gBADIX,UACG,UAAS;AAAA,IACd,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAsVH,SAAOA;AACT,GAAC;;"}